package com.university.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.finance.entity.Finance;
import com.university.finance.mapper.FinanceMapper;
import com.university.finance.service.FinanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务管理业务逻辑实现类
 */
@Service
public class FinanceServiceImpl extends ServiceImpl<FinanceMapper, Finance> implements FinanceService {

    @Autowired
    private FinanceMapper financeMapper;

    @Override
    public List<Finance> getByStudentId(Long studentId) {
        QueryWrapper<Finance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", studentId);
        queryWrapper.orderByDesc("transaction_time");
        return financeMapper.selectList(queryWrapper);
    }

    @Override
    public List<Finance> getByTransactionType(Integer transactionType) {
        QueryWrapper<Finance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transaction_type", transactionType);
        queryWrapper.orderByDesc("transaction_time");
        return financeMapper.selectList(queryWrapper);
    }

    @Override
    public List<Finance> getByStatus(Integer status) {
        QueryWrapper<Finance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        queryWrapper.orderByDesc("create_time");
        return financeMapper.selectList(queryWrapper);
    }

    @Override
    public Page<Finance> getFinancePage(Page<Finance> page, Finance finance) {
        QueryWrapper<Finance> queryWrapper = new QueryWrapper<>(finance);
        queryWrapper.orderByDesc("create_time");
        return financeMapper.selectPage(page, queryWrapper);
    }

    @Override
    public boolean updateFinanceStatus(Long id, Integer status) {
        Finance finance = new Finance();
        finance.setId(id);
        finance.setStatus(status);
        return updateById(finance);
    }

    @Override
    public Map<String, Object> generateStudentFinancialReport(Long studentId) {
        Map<String, Object> report = new HashMap<>();
        List<Finance> financeList = getByStudentId(studentId);

        // 计算总收入和总支出
        BigDecimal income = BigDecimal.ZERO;
        BigDecimal expense = BigDecimal.ZERO;

        for (Finance finance : financeList) {
            BigDecimal amount = finance.getAmount();
            if (finance.getTransactionType() == 2) { // 奖学金为收入
                income = income.add(amount);
            } else { // 其他类型为支出
                expense = expense.add(amount);
            }
        }

        report.put("totalIncome", income);
        report.put("totalExpense", expense);
        report.put("balance", income.subtract(expense));
        report.put("transactionCount", financeList.size());
        report.put("recentTransactions", financeList.stream().limit(5).toList());

        return report;
    }

    @Override
    public BigDecimal getTotalAmountByType(Integer transactionType) {
        QueryWrapper<Finance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transaction_type", transactionType);
        queryWrapper.eq("status", 1); // 只统计已支付的
        return financeMapper.selectObjs(queryWrapper)
                .stream()
                .map(obj -> (BigDecimal) obj)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}