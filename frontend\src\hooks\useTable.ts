import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import { PageResponse } from '../types';

interface UseTableOptions<T> {
  fetchData: (params: any) => Promise<PageResponse<T>>;
  initialPageSize?: number;
  onError?: (error: any) => void;
}

interface TableState {
  current: number;
  pageSize: number;
  total: number;
}

interface UseTableReturn<T> {
  data: T[];
  loading: boolean;
  pagination: TableState;
  searchParams: Record<string, any>;
  setSearchParams: (params: Record<string, any>) => void;
  refresh: () => void;
  handleTableChange: (page: number, pageSize?: number) => void;
  resetSearch: () => void;
}

export function useTable<T = any>(
  options: UseTableOptions<T>
): UseTableReturn<T> {
  const { fetchData, initialPageSize = 10, onError } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<TableState>({
    current: 1,
    pageSize: initialPageSize,
    total: 0
  });
  const [searchParams, setSearchParams] = useState<Record<string, any>>({});

  const loadData = useCallback(async (params?: Record<string, any>) => {
    setLoading(true);
    try {
      const requestParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
        ...params
      };

      const response = await fetchData(requestParams);
      
      setData(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error: any) {
      const errorMessage = error.message || '获取数据失败';
      message.error(errorMessage);
      
      if (onError) {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [fetchData, pagination.current, pagination.pageSize, searchParams, onError]);

  const refresh = useCallback(() => {
    loadData();
  }, [loadData]);

  const handleTableChange = useCallback((page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize
    }));
  }, []);

  const updateSearchParams = useCallback((params: Record<string, any>) => {
    setSearchParams(params);
    setPagination(prev => ({
      ...prev,
      current: 1 // 重置到第一页
    }));
  }, []);

  const resetSearch = useCallback(() => {
    setSearchParams({});
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
  }, []);

  // 当分页或搜索参数变化时重新加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    loading,
    pagination,
    searchParams,
    setSearchParams: updateSearchParams,
    refresh,
    handleTableChange,
    resetSearch
  };
}
