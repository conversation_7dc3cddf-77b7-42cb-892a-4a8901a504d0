.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.login-content {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

/* 表单样式增强 */
.login-card .ant-form-item {
  margin-bottom: 20px;
}

.login-card .ant-input-affix-wrapper,
.login-card .ant-input {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.login-card .ant-input-affix-wrapper:hover,
.login-card .ant-input:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.login-card .ant-input-affix-wrapper:focus,
.login-card .ant-input-affix-wrapper-focused,
.login-card .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-card .ant-btn-primary {
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-card .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, #5cdbd3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.login-card .ant-btn-primary:active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    max-width: 350px;
    padding: 16px;
  }
  
  .login-card {
    padding: 32px 24px;
  }
  
  .login-logo {
    width: 60px;
    height: 60px;
  }
  
  .login-logo .anticon {
    font-size: 36px !important;
  }
}

@media (max-width: 480px) {
  .login-content {
    max-width: 320px;
    padding: 12px;
  }
  
  .login-card {
    padding: 24px 20px;
  }
}
