package com.university.teacher.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.teacher.entity.Teacher;

import java.util.List;

/**
 * 教师管理业务逻辑层接口
 */
public interface TeacherService extends IService<Teacher> {

    /**
     * 根据教师工号查询教师信息
     */
    Teacher getByTeacherNo(String teacherNo);

    /**
     * 根据学院ID查询教师列表
     */
    List<Teacher> getByCollegeId(Long collegeId);

    /**
     * 根据专业ID查询教师列表
     */
    List<Teacher> getByMajorId(Long majorId);

    /**
     * 分页查询教师列表
     */
    IPage<Teacher> getTeacherPage(Page<Teacher> page, Teacher teacher);

    /**
     * 更新教师状态
     */
    boolean updateTeacherStatus(Long id, Integer status);

    /**
     * 获取教师详细信息
     */
    Teacher getTeacherDetail(Long id);
}