import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  UserOutlined,
  TeamOutlined,
  BookOutlined
} from '@ant-design/icons';
import { Gender } from '../types';
import type { Teacher, PageResponse } from '../types';
import { teacherService } from '../services/teacherService';
import dayjs from 'dayjs';

const { Option } = Select;
const { Title } = Typography;

const TeacherManagement: React.FC = () => {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<number | undefined>();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [form] = Form.useForm();

  // 统计数据
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    departments: 0
  });

  useEffect(() => {
    fetchTeachers();
  }, [pagination.current, pagination.pageSize, searchText, selectedStatus]);

  const fetchTeachers = async () => {
    setLoading(true);
    try {
      const params = {
        current: pagination.current,
        size: pagination.pageSize,
        name: searchText || undefined,
        status: selectedStatus
      };

      const response: PageResponse<Teacher> = await teacherService.getTeacherPage(params);
      
      setTeachers(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      // 更新统计数据
      setStatistics({
        total: response.total,
        active: response.records.filter(t => t.status === 1).length,
        inactive: response.records.filter(t => t.status === 0).length,
        departments: new Set(response.records.map(t => t.departmentId)).size
      });

    } catch (error) {
      message.error('获取教师列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingTeacher(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: Teacher) => {
    setEditingTeacher(record);
    form.setFieldsValue({
      ...record,
      birthDate: record.birthDate ? dayjs(record.birthDate) : null
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await teacherService.deleteTeacher(id);
      message.success('删除成功');
      fetchTeachers();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const teacherData = {
        ...values,
        birthDate: values.birthDate ? values.birthDate.format('YYYY-MM-DD') : null
      };

      if (editingTeacher) {
        await teacherService.updateTeacher(editingTeacher.id, teacherData);
        message.success('更新成功');
      } else {
        await teacherService.createTeacher(teacherData);
        message.success('创建成功');
      }

      setModalVisible(false);
      fetchTeachers();
    } catch (error) {
      message.error(editingTeacher ? '更新失败' : '创建失败');
    }
  };

  const handleStatusChange = async (id: number, status: number) => {
    try {
      await teacherService.updateTeacherStatus(id, status);
      message.success('状态更新成功');
      fetchTeachers();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const columns = [
    {
      title: '工号',
      dataIndex: 'teacherNo',
      key: 'teacherNo',
      width: 120,
      fixed: 'left' as const
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 80,
      render: (gender: number) => gender === Gender.MALE ? '男' : '女'
    },
    {
      title: '职称',
      dataIndex: 'title',
      key: 'title',
      width: 120
    },
    {
      title: '学位',
      dataIndex: 'degree',
      key: 'degree',
      width: 100
    },
    {
      title: '学院',
      dataIndex: ['department', 'deptName'],
      key: 'department',
      width: 150
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: Teacher) => (
        <Select
          value={status}
          size="small"
          style={{ width: 80 }}
          onChange={(value) => handleStatusChange(record.id, value)}
        >
          <Option value={1}>
            <Tag color="green">在职</Tag>
          </Option>
          <Option value={0}>
            <Tag color="red">离职</Tag>
          </Option>
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Teacher) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个教师吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="教师总数"
              value={statistics.total}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在职教师"
              value={statistics.active}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="离职教师"
              value={statistics.inactive}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="学院数量"
              value={statistics.departments}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space>
                <Input
                  placeholder="搜索教师姓名或工号"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                  allowClear
                />
                <Select
                  placeholder="选择状态"
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Option value={1}>在职</Option>
                  <Option value={0}>离职</Option>
                </Select>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  新增教师
                </Button>
                <Button icon={<ImportOutlined />}>
                  批量导入
                </Button>
                <Button icon={<ExportOutlined />}>
                  导出数据
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={teachers}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10
              }));
            }
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingTeacher ? '编辑教师' : '新增教师'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="teacherNo"
                label="工号"
                rules={[{ required: true, message: '请输入工号' }]}
              >
                <Input placeholder="请输入工号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="gender"
                label="性别"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Select placeholder="请选择性别">
                  <Option value={Gender.MALE}>男</Option>
                  <Option value={Gender.FEMALE}>女</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="birthDate"
                label="出生日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="departmentId"
                label="学院"
                rules={[{ required: true, message: '请选择学院' }]}
              >
                <Select placeholder="请选择学院">
                  <Option value={1}>计算机学院</Option>
                  <Option value={2}>数学学院</Option>
                  <Option value={3}>物理学院</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="title"
                label="职称"
              >
                <Select placeholder="请选择职称">
                  <Option value="教授">教授</Option>
                  <Option value="副教授">副教授</Option>
                  <Option value="讲师">讲师</Option>
                  <Option value="助教">助教</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="degree"
                label="学位"
              >
                <Select placeholder="请选择学位">
                  <Option value="博士">博士</Option>
                  <Option value="硕士">硕士</Option>
                  <Option value="学士">学士</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value={1}>在职</Option>
                  <Option value={0}>离职</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="specialty"
            label="专业特长"
          >
            <Input.TextArea rows={3} placeholder="请输入专业特长" />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingTeacher ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TeacherManagement;
