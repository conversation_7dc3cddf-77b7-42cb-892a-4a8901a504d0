import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Space, Typo<PERSON>, Button, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  DashboardOutlined,
  TeamOutlined,
  BookOutlined,
  TrophyOutlined,
  DollarOutlined,
  BankOutlined,
  NotificationOutlined,
  Bar<PERSON>hartOutlined,
  HomeOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { UserType } from '../../types';
import type { MenuItem } from '../../types';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { state, logout, hasAnyRole } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 根据用户角色定义菜单项
  const getMenuItems = (): MenuItem[] => {
    const baseItems: MenuItem[] = [
      {
        key: '/dashboard',
        label: '仪表板',
        icon: <DashboardOutlined />,
        path: '/dashboard',
        roles: [UserType.STUDENT, UserType.TEACHER, UserType.ADMIN]
      }
    ];

    const adminItems: MenuItem[] = [
      {
        key: '/students',
        label: '学生管理',
        icon: <TeamOutlined />,
        path: '/students',
        roles: [UserType.ADMIN, UserType.TEACHER]
      },
      {
        key: '/teachers',
        label: '教师管理',
        icon: <UserOutlined />,
        path: '/teachers',
        roles: [UserType.ADMIN]
      },
      {
        key: '/courses',
        label: '课程管理',
        icon: <BookOutlined />,
        path: '/courses',
        roles: [UserType.ADMIN, UserType.TEACHER]
      },
      {
        key: '/grades',
        label: '成绩管理',
        icon: <TrophyOutlined />,
        path: '/grades',
        roles: [UserType.ADMIN, UserType.TEACHER]
      },
      {
        key: '/finance',
        label: '财务管理',
        icon: <DollarOutlined />,
        path: '/finance',
        roles: [UserType.ADMIN]
      },
      {
        key: '/resources',
        label: '资源管理',
        icon: <BankOutlined />,
        path: '/resources',
        roles: [UserType.ADMIN]
      },
      {
        key: '/notifications',
        label: '通知公告',
        icon: <NotificationOutlined />,
        path: '/notifications',
        roles: [UserType.ADMIN]
      },
      {
        key: '/analytics',
        label: '数据分析',
        icon: <BarChartOutlined />,
        path: '/analytics',
        roles: [UserType.ADMIN]
      }
    ];

    const studentItems: MenuItem[] = [
      {
        key: '/my-courses',
        label: '我的课程',
        icon: <BookOutlined />,
        path: '/my-courses',
        roles: [UserType.STUDENT]
      },
      {
        key: '/my-grades',
        label: '我的成绩',
        icon: <TrophyOutlined />,
        path: '/my-grades',
        roles: [UserType.STUDENT]
      },
      {
        key: '/my-finance',
        label: '费用查询',
        icon: <DollarOutlined />,
        path: '/my-finance',
        roles: [UserType.STUDENT]
      }
    ];

    const teacherItems: MenuItem[] = [
      {
        key: '/my-teaching',
        label: '我的教学',
        icon: <BookOutlined />,
        path: '/my-teaching',
        roles: [UserType.TEACHER]
      }
    ];

    // 根据用户角色过滤菜单项
    const allItems = [...baseItems, ...adminItems, ...studentItems, ...teacherItems];
    return allItems.filter(item => 
      !item.roles || hasAnyRole(item.roles)
    );
  };

  const menuItems = getMenuItems();

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人信息',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      label: '系统设置',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout
    }
  ];

  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  }

  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = menuItems.find(item => item.key === key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  const getUserTypeText = (userType: number) => {
    switch (userType) {
      case UserType.STUDENT:
        return '学生';
      case UserType.TEACHER:
        return '教师';
      case UserType.ADMIN:
        return '管理员';
      default:
        return '用户';
    }
  };

  return (
    <Layout className="main-layout">
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="layout-sider"
        width={240}
        collapsedWidth={80}
      >
        <div className="logo">
          <HomeOutlined className="logo-icon" />
          {!collapsed && <span className="logo-text">大学管理系统</span>}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          onClick={handleMenuClick}
          items={menuItems.map(item => ({
            key: item.key,
            icon: item.icon,
            label: item.label
          }))}
        />
      </Sider>
      
      <Layout className="site-layout">
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="trigger"
            />
          </div>
          
          <div className="header-right">
            <Space size="large">
              <Badge count={5} size="small">
                <Button 
                  type="text" 
                  icon={<BellOutlined />} 
                  className="header-action"
                />
              </Badge>
              
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="user-info">
                  <Space>
                    <Avatar 
                      size="small" 
                      icon={<UserOutlined />}
                      src={state.user?.avatar}
                    />
                    <div className="user-details">
                      <Text className="user-name">
                        {state.user?.realName || state.user?.username}
                      </Text>
                      <Text type="secondary" className="user-role">
                        {getUserTypeText(state.user?.userType || 0)}
                      </Text>
                    </div>
                  </Space>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>
        
        <Content className="layout-content">
          <div className="content-wrapper">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
