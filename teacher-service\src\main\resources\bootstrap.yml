spring:
  application:
    name: teacher-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml
        shared-configs:
          - data-id: common.yml
            refresh: true
  profiles:
    active: dev

server:
  port: 8083

logging:
  level:
    root: INFO
    com.university.teacher: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.cloud: INFO
    com.alibaba.nacos: INFO
    com.baomidou.mybatisplus: INFO