.main-layout {
  height: 100vh;
}

.layout-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-left: 12px;
}

.site-layout {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.main-layout .ant-layout-sider-collapsed + .site-layout {
  margin-left: 80px;
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
  color: rgba(0, 0, 0, 0.65);
}

.trigger:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-action {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
  transition: all 0.3s;
  padding: 8px;
  border-radius: 6px;
}

.header-action:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.user-info {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.user-info:hover {
  background: rgba(24, 144, 255, 0.1);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.user-name {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.user-role {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.layout-content {
  margin: 0;
  padding: 0;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px - 48px);
}

/* 菜单样式增强 */
.layout-sider .ant-menu {
  flex: 1;
  border-right: none;
}

.layout-sider .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.layout-sider .ant-menu-item-selected {
  background: linear-gradient(135deg, #1890ff, #36cfc9) !important;
  color: white !important;
}

.layout-sider .ant-menu-item-selected .anticon {
  color: white !important;
}

.layout-sider .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.layout-sider .ant-menu-item:hover .anticon {
  color: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sider.ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }
  
  .site-layout {
    margin-left: 0 !important;
  }
  
  .layout-header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .user-details {
    display: none;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 12px;
  }
  
  .content-wrapper {
    padding: 12px;
  }
  
  .trigger {
    padding: 0 12px;
  }
}

/* 滚动条样式 */
.layout-sider .ant-layout-sider-children::-webkit-scrollbar {
  width: 6px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
.layout-sider .ant-menu-item {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.layout-sider .ant-menu-item::after {
  display: none;
}

/* Badge 样式调整 */
.header-right .ant-badge {
  display: flex;
  align-items: center;
}

/* Dropdown 样式 */
.ant-dropdown {
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.ant-dropdown .ant-dropdown-menu {
  border-radius: 8px;
  padding: 8px;
}

.ant-dropdown .ant-dropdown-menu-item {
  border-radius: 6px;
  margin: 2px 0;
}

.ant-dropdown .ant-dropdown-menu-item:hover {
  background: rgba(24, 144, 255, 0.1);
}
