-- 大学管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS university_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE university_management;

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    user_type TINYINT NOT NULL COMMENT '用户类型 1-学生 2-教师 3-管理员',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '角色表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role_id)
) COMMENT '用户角色关联表';

-- 学院表
CREATE TABLE IF NOT EXISTS department (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    dept_name VARCHAR(100) NOT NULL COMMENT '学院名称',
    dept_code VARCHAR(20) NOT NULL UNIQUE COMMENT '学院编码',
    description TEXT COMMENT '学院描述',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '学院表';

-- 专业表
CREATE TABLE IF NOT EXISTS major (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    major_name VARCHAR(100) NOT NULL COMMENT '专业名称',
    major_code VARCHAR(20) NOT NULL UNIQUE COMMENT '专业编码',
    department_id BIGINT NOT NULL COMMENT '所属学院ID',
    degree_type TINYINT NOT NULL COMMENT '学位类型 1-本科 2-硕士 3-博士',
    duration TINYINT DEFAULT 4 COMMENT '学制年限',
    description TEXT COMMENT '专业描述',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '专业表';

-- 班级表
CREATE TABLE IF NOT EXISTS class (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL COMMENT '班级名称',
    class_code VARCHAR(20) NOT NULL UNIQUE COMMENT '班级编码',
    major_id BIGINT NOT NULL COMMENT '所属专业ID',
    enrollment_year INT NOT NULL COMMENT '入学年份',
    advisor_id BIGINT COMMENT '班主任ID',
    max_students INT DEFAULT 50 COMMENT '最大学生数',
    current_students INT DEFAULT 0 COMMENT '当前学生数',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (major_id) REFERENCES major(id)
) COMMENT '班级表';

-- 学生表
CREATE TABLE IF NOT EXISTS student (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    student_no VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender TINYINT NOT NULL COMMENT '性别 1-男 2-女',
    birth_date DATE COMMENT '出生日期',
    id_card VARCHAR(18) COMMENT '身份证号',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    address TEXT COMMENT '家庭住址',
    major_id BIGINT NOT NULL COMMENT '专业ID',
    class_id BIGINT NOT NULL COMMENT '班级ID',
    enrollment_year INT NOT NULL COMMENT '入学年份',
    graduation_year INT COMMENT '毕业年份',
    student_status TINYINT DEFAULT 1 COMMENT '学籍状态 1-在读 2-休学 3-退学 4-毕业',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    FOREIGN KEY (major_id) REFERENCES major(id),
    FOREIGN KEY (class_id) REFERENCES class(id)
) COMMENT '学生表';

-- 教师表
CREATE TABLE IF NOT EXISTS teacher (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    teacher_no VARCHAR(20) NOT NULL UNIQUE COMMENT '工号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender TINYINT NOT NULL COMMENT '性别 1-男 2-女',
    birth_date DATE COMMENT '出生日期',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    department_id BIGINT NOT NULL COMMENT '所属学院ID',
    title VARCHAR(50) COMMENT '职称',
    education VARCHAR(50) COMMENT '学历',
    hire_date DATE COMMENT '入职日期',
    status TINYINT DEFAULT 1 COMMENT '状态 0-离职 1-在职',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '教师表';

-- 课程表
CREATE TABLE IF NOT EXISTS course (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL UNIQUE COMMENT '课程编码',
    course_name VARCHAR(100) NOT NULL COMMENT '课程名称',
    credits DECIMAL(3,1) NOT NULL COMMENT '学分',
    course_type TINYINT NOT NULL COMMENT '课程类型 1-必修 2-选修',
    department_id BIGINT NOT NULL COMMENT '开课学院ID',
    teacher_id BIGINT COMMENT '授课教师ID',
    semester VARCHAR(20) COMMENT '开课学期',
    class_hours INT COMMENT '课时数',
    max_students INT DEFAULT 100 COMMENT '最大选课人数',
    current_students INT DEFAULT 0 COMMENT '当前选课人数',
    description TEXT COMMENT '课程描述',
    status TINYINT DEFAULT 1 COMMENT '状态 0-停开 1-开课',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(id)
) COMMENT '课程表';

-- 成绩表
CREATE TABLE IF NOT EXISTS grade (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    score DECIMAL(5,2) COMMENT '成绩',
    grade_point DECIMAL(3,2) COMMENT '绩点',
    semester VARCHAR(20) NOT NULL COMMENT '学期',
    exam_type VARCHAR(20) COMMENT '考试类型',
    exam_date DATE COMMENT '考试日期',
    status TINYINT DEFAULT 1 COMMENT '状态 1-正常 2-重修',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES student(id),
    FOREIGN KEY (course_id) REFERENCES course(id),
    UNIQUE KEY uk_student_course_semester (student_id, course_id, semester)
) COMMENT '成绩表';

-- 学费表
CREATE TABLE IF NOT EXISTS tuition (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    academic_year VARCHAR(20) NOT NULL COMMENT '学年',
    tuition_amount DECIMAL(10,2) NOT NULL COMMENT '学费金额',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已缴金额',
    payment_status TINYINT DEFAULT 0 COMMENT '缴费状态 0-未缴 1-已缴 2-部分缴费',
    due_date DATE COMMENT '缴费截止日期',
    payment_date DATE COMMENT '缴费日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES student(id)
) COMMENT '学费表';

-- 插入初始数据
-- 插入角色数据
INSERT INTO sys_role (role_name, role_code, description) VALUES
('管理员', 'ROLE_ADMIN', '系统管理员'),
('教师', 'ROLE_TEACHER', '教师用户'),
('学生', 'ROLE_STUDENT', '学生用户');

-- 插入管理员用户 (密码: 123456)
INSERT INTO sys_user (username, password, real_name, email, user_type) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYqYdYhOBVh706UOFpFMy', '系统管理员', '<EMAIL>', 3);

-- 插入用户角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES (1, 1);

-- 插入学院数据
INSERT INTO department (dept_name, dept_code, description) VALUES
('计算机科学与技术学院', 'CS', '计算机科学与技术学院'),
('数学与统计学院', 'MATH', '数学与统计学院'),
('外国语学院', 'FL', '外国语学院');

-- 插入专业数据
INSERT INTO major (major_name, major_code, department_id, degree_type, duration) VALUES
('计算机科学与技术', 'CS01', 1, 1, 4),
('软件工程', 'CS02', 1, 1, 4),
('数学与应用数学', 'MATH01', 2, 1, 4),
('英语', 'FL01', 3, 1, 4);

-- 插入班级数据
INSERT INTO class (class_name, class_code, major_id, enrollment_year) VALUES
('计科2024-1班', 'CS2024-1', 1, 2024),
('软工2024-1班', 'SE2024-1', 2, 2024);

COMMIT;
