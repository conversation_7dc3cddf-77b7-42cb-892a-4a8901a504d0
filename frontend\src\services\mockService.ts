import { Student, Teacher, Course, User, UserType, Gender, StudentStatus } from '../types';

// Mock用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 1,
    userType: UserType.ADMIN,
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 2,
    username: 'teacher',
    realName: '张教授',
    email: '<EMAIL>',
    phone: '13800138001',
    status: 1,
    userType: UserType.TEACHER,
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 3,
    username: 'student',
    realName: '李小明',
    email: '<EMAIL>',
    phone: '13800138002',
    status: 1,
    userType: UserType.STUDENT,
    createTime: '2024-01-01 00:00:00'
  }
];

// Mock学生数据
export const mockStudents: Student[] = [
  {
    id: 1,
    userId: 3,
    studentNo: '2021001001',
    name: '李小明',
    gender: Gender.MALE,
    birthDate: '2003-05-15',
    phone: '13800138002',
    email: '<EMAIL>',
    address: '北京市海淀区中关村大街1号',
    majorId: 1,
    classId: 1,
    enrollmentYear: 2021,
    studentStatus: StudentStatus.ENROLLED,
    createTime: '2024-01-01 00:00:00',
    major: {
      id: 1,
      majorName: '计算机科学与技术',
      majorCode: 'CS',
      departmentId: 1,
      degreeType: 1,
      duration: 4,
      status: 1
    },
    class: {
      id: 1,
      className: '计科2021-1班',
      classCode: 'CS2021-1',
      majorId: 1,
      enrollmentYear: 2021,
      maxStudents: 50,
      currentStudents: 45,
      status: 1
    }
  },
  {
    id: 2,
    userId: 4,
    studentNo: '2021001002',
    name: '王小红',
    gender: Gender.FEMALE,
    birthDate: '2003-08-20',
    phone: '13800138003',
    email: '<EMAIL>',
    address: '上海市浦东新区张江高科技园区',
    majorId: 1,
    classId: 1,
    enrollmentYear: 2021,
    studentStatus: StudentStatus.ENROLLED,
    createTime: '2024-01-01 00:00:00',
    major: {
      id: 1,
      majorName: '计算机科学与技术',
      majorCode: 'CS',
      departmentId: 1,
      degreeType: 1,
      duration: 4,
      status: 1
    },
    class: {
      id: 1,
      className: '计科2021-1班',
      classCode: 'CS2021-1',
      majorId: 1,
      enrollmentYear: 2021,
      maxStudents: 50,
      currentStudents: 45,
      status: 1
    }
  }
];

// Mock教师数据
export const mockTeachers: Teacher[] = [
  {
    id: 1,
    userId: 2,
    teacherNo: 'T001001',
    name: '张教授',
    gender: Gender.MALE,
    birthDate: '1975-03-10',
    phone: '13800138001',
    email: '<EMAIL>',
    departmentId: 1,
    title: '教授',
    degree: '博士',
    specialty: '人工智能、机器学习',
    status: 1,
    createTime: '2024-01-01 00:00:00',
    department: {
      id: 1,
      deptName: '计算机学院',
      deptCode: 'CS',
      status: 1
    }
  },
  {
    id: 2,
    userId: 5,
    teacherNo: 'T001002',
    name: '李副教授',
    gender: Gender.FEMALE,
    birthDate: '1980-07-25',
    phone: '13800138004',
    email: '<EMAIL>',
    departmentId: 1,
    title: '副教授',
    degree: '博士',
    specialty: '数据库系统、大数据技术',
    status: 1,
    createTime: '2024-01-01 00:00:00',
    department: {
      id: 1,
      deptName: '计算机学院',
      deptCode: 'CS',
      status: 1
    }
  }
];

// Mock课程数据
export const mockCourses: Course[] = [
  {
    id: 1,
    courseNo: 'CS101',
    courseName: '计算机科学导论',
    credits: 3,
    hours: 48,
    courseType: 1, // 必修
    departmentId: 1,
    teacherId: 1,
    semester: '2024春季',
    maxStudents: 100,
    currentStudents: 85,
    description: '计算机科学基础课程，介绍计算机科学的基本概念和原理',
    status: 1,
    createTime: '2024-01-01 00:00:00',
    teacher: {
      id: 1,
      userId: 2,
      teacherNo: 'T001001',
      name: '张教授',
      gender: Gender.MALE,
      phone: '13800138001',
      email: '<EMAIL>',
      departmentId: 1,
      title: '教授',
      degree: '博士',
      status: 1,
      createTime: '2024-01-01 00:00:00'
    },
    department: {
      id: 1,
      deptName: '计算机学院',
      deptCode: 'CS',
      status: 1
    }
  },
  {
    id: 2,
    courseNo: 'CS201',
    courseName: '数据结构与算法',
    credits: 4,
    hours: 64,
    courseType: 1, // 必修
    departmentId: 1,
    teacherId: 2,
    semester: '2024春季',
    maxStudents: 80,
    currentStudents: 75,
    description: '数据结构和算法设计与分析',
    status: 1,
    createTime: '2024-01-01 00:00:00',
    teacher: {
      id: 2,
      userId: 5,
      teacherNo: 'T001002',
      name: '李副教授',
      gender: Gender.FEMALE,
      phone: '13800138004',
      email: '<EMAIL>',
      departmentId: 1,
      title: '副教授',
      degree: '博士',
      status: 1,
      createTime: '2024-01-01 00:00:00'
    },
    department: {
      id: 1,
      deptName: '计算机学院',
      deptCode: 'CS',
      status: 1
    }
  }
];

// Mock API响应生成器
export const createMockResponse = <T>(data: T, success: boolean = true, message: string = 'success') => {
  return {
    success,
    code: success ? 200 : 500,
    message,
    data
  };
};

// Mock分页响应生成器
export const createMockPageResponse = <T>(
  data: T[],
  current: number = 1,
  size: number = 10,
  total?: number
) => {
  const actualTotal = total || data.length;
  const startIndex = (current - 1) * size;
  const endIndex = startIndex + size;
  const records = data.slice(startIndex, endIndex);

  return createMockResponse({
    records,
    total: actualTotal,
    size,
    current,
    pages: Math.ceil(actualTotal / size)
  });
};

// 延迟函数，模拟网络请求
export const delay = (ms: number = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Mock登录验证
export const mockLogin = async (username: string, password: string) => {
  await delay(1000);
  
  const user = mockUsers.find(u => u.username === username);
  
  if (!user || password !== '123456') {
    throw new Error('用户名或密码错误');
  }
  
  return {
    accessToken: 'mock-access-token-' + Date.now(),
    refreshToken: 'mock-refresh-token-' + Date.now()
  };
};
