package com.university.notification.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.notification.entity.Notification;

import java.util.List;

/**
 * 通知公告管理业务逻辑层接口
 */
public interface NotificationService extends IService<Notification> {

    /**
     * 根据接收者ID查询通知列表
     */
    List<Notification> getByRecipientId(Long recipientId);

    /**
     * 根据通知类型查询通知列表
     */
    List<Notification> getByNotificationType(Integer notificationType);

    /**
     * 根据状态查询通知列表
     */
    List<Notification> getByStatus(Integer status);

    /**
     * 分页查询通知列表
     */
    Page<Notification> getNotificationPage(Page<Notification> page, Notification notification);

    /**
     * 更新通知状态
     */
    boolean updateNotificationStatus(Long id, Integer status);

    /**
     * 批量更新通知状态
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 查询未读通知数量
     */
    Integer getUnreadCount(Long recipientId);

    /**
     * 发送系统通知
     */
    boolean sendSystemNotification(Notification notification);
}