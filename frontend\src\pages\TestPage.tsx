import React, { useState } from 'react';
import { Card, Button, Space, message, Typography, Divider } from 'antd';
import { authService } from '../services/authService';
import { studentService } from '../services/studentService';

const { Title, Text } = Typography;

const TestPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const testLogin = async () => {
    setLoading(true);
    try {
      const result = await authService.login({
        username: 'admin',
        password: '123456'
      });
      addResult('✅ 登录测试成功');
      message.success('登录测试成功');
    } catch (error: any) {
      addResult(`❌ 登录测试失败: ${error.message}`);
      message.error('登录测试失败');
    } finally {
      setLoading(false);
    }
  };

  const testStudentAPI = async () => {
    setLoading(true);
    try {
      const result = await studentService.getStudentPage({
        current: 1,
        size: 10
      });
      addResult('✅ 学生API测试成功');
      message.success('学生API测试成功');
    } catch (error: any) {
      addResult(`❌ 学生API测试失败: ${error.message}`);
      message.error('学生API测试失败');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>API 连接测试</Title>
        <Text type="secondary">
          测试前端与后端API的连接状态
        </Text>
        
        <Divider />
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Button 
              type="primary" 
              onClick={testLogin}
              loading={loading}
            >
              测试登录API
            </Button>
            <Button 
              onClick={testStudentAPI}
              loading={loading}
            >
              测试学生API
            </Button>
            <Button 
              onClick={clearResults}
              disabled={testResults.length === 0}
            >
              清除结果
            </Button>
          </Space>
          
          {testResults.length > 0 && (
            <Card title="测试结果" size="small">
              {testResults.map((result, index) => (
                <div key={index} style={{ marginBottom: '8px' }}>
                  <Text code>{result}</Text>
                </div>
              ))}
            </Card>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default TestPage;
