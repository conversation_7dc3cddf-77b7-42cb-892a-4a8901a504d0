package com.university.finance.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.finance.entity.Finance;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 财务管理业务逻辑层接口
 */
public interface FinanceService extends IService<Finance> {

    /**
     * 根据学生ID查询财务记录列表
     */
    List<Finance> getByStudentId(Long studentId);

    /**
     * 根据交易类型查询财务记录列表
     */
    List<Finance> getByTransactionType(Integer transactionType);

    /**
     * 根据状态查询财务记录列表
     */
    List<Finance> getByStatus(Integer status);

    /**
     * 分页查询财务记录列表
     */
    Page<Finance> getFinancePage(Page<Finance> page, Finance finance);

    /**
     * 更新财务记录状态
     */
    boolean updateFinanceStatus(Long id, Integer status);

    /**
     * 生成学生财务报表
     */
    Map<String, Object> generateStudentFinancialReport(Long studentId);

    /**
     * 统计指定类型交易总额
     */
    BigDecimal getTotalAmountByType(Integer transactionType);
}