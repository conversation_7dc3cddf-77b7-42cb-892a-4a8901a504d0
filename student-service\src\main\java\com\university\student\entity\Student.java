package com.university.student.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生实体类
 */
@Data
@TableName("sys_student")
public class Student {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String studentNo; // 学号
    private String realName; // 姓名
    private Integer gender; // 性别：0-未知 1-男 2-女
    private LocalDate birthDate; // 出生日期
    private String idCard; // 身份证号
    private String nation; // 民族
    private String politicalStatus; // 政治面貌
    private String admissionDate; // 入学日期
    private String graduationDate; // 毕业日期
    private Long collegeId; // 学院ID
    private String collegeName; // 学院名称
    private Long majorId; // 专业ID
    private String majorName; // 专业名称
    private Long classId; // 班级ID
    private String className; // 班级名称
    private String educationLevel; // 学历层次
    private String studentStatus; // 学籍状态：1-在读 2-休学 3-退学 4-毕业
    private String contactPhone; // 联系电话
    private String emergencyContact; // 紧急联系人
    private String emergencyPhone; // 紧急联系电话
    private String address; // 家庭地址
    private String email; // 邮箱
    private String avatar; // 头像
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private String remark; // 备注

    // @Data注解会自动生成所有getter/setter方法
}