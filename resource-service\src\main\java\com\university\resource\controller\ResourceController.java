package com.university.resource.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.resource.entity.Resource;
import com.university.resource.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * 资源管理控制器
 */
@RestController
@RequestMapping("/resource")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    /**
     * 根据ID查询资源信息
     */
    @GetMapping("/{id}")
    public Resource getById(@PathVariable Long id) {
        return resourceService.getById(id);
    }

    /**
     * 根据课程ID查询资源列表
     */
    @GetMapping("/course/{courseId}")
    public List<Resource> getByCourseId(@PathVariable Long courseId) {
        return resourceService.getByCourseId(courseId);
    }

    /**
     * 根据上传者ID查询资源列表
     */
    @GetMapping("/uploader/{uploaderId}")
    public List<Resource> getByUploaderId(@PathVariable Long uploaderId) {
        return resourceService.getByUploaderId(uploaderId);
    }

    /**
     * 根据资源类型查询资源列表
     */
    @GetMapping("/type/{resourceType}")
    public List<Resource> getByResourceType(@PathVariable Integer resourceType) {
        return resourceService.getByResourceType(resourceType);
    }

    /**
     * 分页查询资源列表
     */
    @GetMapping("/page")
    public Page<Resource> getPage(@RequestParam(defaultValue = "1") long current,
                                  @RequestParam(defaultValue = "10") long size,
                                  Resource resource) {
        Page<Resource> page = new Page<>(current, size);
        return resourceService.getResourcePage(page, resource);
    }

    /**
     * 上传资源
     */
    @PostMapping("/upload")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public boolean upload(@RequestParam("file") MultipartFile file, Resource resource) {
        // 文件上传逻辑将在后续实现
        return resourceService.save(resource);
    }

    /**
     * 更新资源信息
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public boolean update(@RequestBody Resource resource) {
        return resourceService.updateById(resource);
    }

    /**
     * 更新资源状态
     */
    @PutMapping("/status")
    @PreAuthorize("hasRole('ADMIN')")
    public boolean updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        return resourceService.updateResourceStatus(id, status);
    }

    /**
     * 增加下载次数
     */
    @PutMapping("/download/{id}")
    public boolean incrementDownloadCount(@PathVariable Long id) {
        return resourceService.incrementDownloadCount(id);
    }

    /**
     * 删除资源
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @resourceService.getById(#id).getUploaderId() == authentication.principal.id")
    public boolean delete(@PathVariable Long id) {
        return resourceService.removeById(id);
    }
}