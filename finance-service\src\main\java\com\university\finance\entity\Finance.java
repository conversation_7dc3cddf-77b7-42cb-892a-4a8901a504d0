package com.university.finance.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 财务记录实体类
 */
@Data
@TableName("sys_finance")
public class Finance {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 交易编号
     */
    private String transactionNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 交易类型(0-学费,1-住宿费,2-奖学金,3-其他)
     */
    private Integer transactionType;

    /**
     * 支付方式(0-支付宝,1-微信,2-银行卡,3-现金)
     */
    private Integer paymentMethod;

    /**
     * 交易状态(0-未支付,1-已支付,2-已退款,3-已取消)
     */
    private Integer status;

    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}