package com.university.grade.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.grade.entity.Grade;

import java.util.List;

/**
 * 成绩管理业务逻辑层接口
 */
public interface GradeService extends IService<Grade> {

    /**
     * 根据学生ID查询成绩列表
     */
    List<Grade> getByStudentId(Long studentId);

    /**
     * 根据课程ID查询成绩列表
     */
    List<Grade> getByCourseId(Long courseId);

    /**
     * 根据学生ID和课程ID查询成绩
     */
    Grade getByStudentIdAndCourseId(Long studentId, Long courseId);

    /**
     * 分页查询成绩列表
     */
    Page<Grade> getGradePage(Page<Grade> page, Grade grade);

    /**
     * 更新成绩状态
     */
    boolean updateGradeStatus(Long id, Integer status);

    /**
     * 批量导入成绩
     */
    boolean importGrades(List<Grade> gradeList);
}