spring:
  application:
    name: logistics-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml
        shared-configs:
          - data-id: common.yml
            refresh: true
  profiles:
    active: dev

server:
  port: 8086

logging:
  level:
    root: INFO
    com.university.logistics: DEBUG
    com.alibaba.nacos: INFO
    org.springframework.security: INFO