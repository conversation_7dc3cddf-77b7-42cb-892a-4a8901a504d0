spring:
  application:
    name: auth-service-test
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: 7121020qing
  # 暂时禁用Redis
  # redis:
  #   host: localhost
  #   port: 6379
  #   password: 
  #   database: 0
  #   timeout: 2000

server:
  port: 8081

# 暂时禁用Nacos
# cloud:
#   nacos:
#     discovery:
#       enabled: false
#     config:
#       enabled: false

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.university.auth.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  token:
    header: Authorization
    prefix: Bearer 
    expiration: 86400000
    refresh-expiration: 604800000
    secret: university_test_secret_key_for_jwt_token_generation

# 日志配置
logging:
  level:
    com.university.auth.mapper: DEBUG
    com.university.auth.service: INFO
    com.university.auth.controller: INFO
    root: INFO
