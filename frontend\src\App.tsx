import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import StudentManagement from './pages/StudentManagement';
import TeacherManagement from './pages/TeacherManagement';
import CourseManagement from './pages/CourseManagement';
import GradeManagement from './pages/GradeManagement';
import Analytics from './pages/Analytics';
import NotificationManagement from './pages/NotificationManagement';
import Profile from './pages/Profile';
import NotFound from './pages/NotFound';
import { UserType } from './types';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';

// 设置dayjs为中文
dayjs.locale('zh-cn');

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <Routes>
            {/* 登录页面 */}
            <Route path="/login" element={<Login />} />

            {/* 受保护的路由 */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              {/* 默认重定向到仪表板 */}
              <Route index element={<Navigate to="/dashboard" replace />} />

              {/* 仪表板 - 所有角色都可以访问 */}
              <Route path="dashboard" element={<Dashboard />} />

              {/* 学生管理 - 管理员和教师可以访问 */}
              <Route
                path="students"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN, UserType.TEACHER]}>
                    <StudentManagement />
                  </ProtectedRoute>
                }
              />

              {/* 教师管理 - 仅管理员可以访问 */}
              <Route
                path="teachers"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <TeacherManagement />
                  </ProtectedRoute>
                }
              />

              {/* 课程管理 - 管理员和教师可以访问 */}
              <Route
                path="courses"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN, UserType.TEACHER]}>
                    <CourseManagement />
                  </ProtectedRoute>
                }
              />

              {/* 成绩管理 - 管理员和教师可以访问 */}
              <Route
                path="grades"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN, UserType.TEACHER]}>
                    <GradeManagement />
                  </ProtectedRoute>
                }
              />

              {/* 财务管理 - 仅管理员可以访问 */}
              <Route
                path="finance"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <div>财务管理页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              {/* 资源管理 - 仅管理员可以访问 */}
              <Route
                path="resources"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <div>资源管理页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              {/* 通知公告 - 仅管理员可以访问 */}
              <Route
                path="notifications"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <NotificationManagement />
                  </ProtectedRoute>
                }
              />

              {/* 数据分析 - 仅管理员可以访问 */}
              <Route
                path="analytics"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <Analytics />
                  </ProtectedRoute>
                }
              />

              {/* 学生专用页面 */}
              <Route
                path="my-courses"
                element={
                  <ProtectedRoute requiredRoles={[UserType.STUDENT]}>
                    <div>我的课程页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="my-grades"
                element={
                  <ProtectedRoute requiredRoles={[UserType.STUDENT]}>
                    <div>我的成绩页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="my-finance"
                element={
                  <ProtectedRoute requiredRoles={[UserType.STUDENT]}>
                    <div>费用查询页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              {/* 教师专用页面 */}
              <Route
                path="my-teaching"
                element={
                  <ProtectedRoute requiredRoles={[UserType.TEACHER]}>
                    <div>我的教学页面（待开发）</div>
                  </ProtectedRoute>
                }
              />

              {/* 个人信息 - 所有角色都可以访问 */}
              <Route path="profile" element={<Profile />} />

              {/* 系统设置 - 仅管理员可以访问 */}
              <Route
                path="settings"
                element={
                  <ProtectedRoute requiredRoles={[UserType.ADMIN]}>
                    <div>系统设置页面（待开发）</div>
                  </ProtectedRoute>
                }
              />
            </Route>

            {/* 404页面 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
