package com.university.logistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.logistics.entity.Maintenance;

import java.util.List;

/**
 * 维修申请管理业务逻辑层接口
 */
public interface MaintenanceService extends IService<Maintenance> {

    /**
     * 根据申请人ID查询维修申请列表
     */
    List<Maintenance> getByApplicantId(Long applicantId);

    /**
     * 根据状态查询维修申请列表
     */
    List<Maintenance> getByStatus(Integer status);

    /**
     * 根据维修类型查询维修申请列表
     */
    List<Maintenance> getByMaintenanceType(Integer maintenanceType);

    /**
     * 分页查询维修申请列表
     */
    Page<Maintenance> getMaintenancePage(Page<Maintenance> page, Maintenance maintenance);

    /**
     * 更新维修申请状态
     */
    boolean updateMaintenanceStatus(Long id, Integer status, Long handlerId);

    /**
     * 分配维修任务
     */
    boolean assignMaintenanceTask(Long id, Long handlerId);
}