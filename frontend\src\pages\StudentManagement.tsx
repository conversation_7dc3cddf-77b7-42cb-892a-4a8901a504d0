import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  UserOutlined,
  TeamOutlined,
  BookOutlined
} from '@ant-design/icons';
import { StudentStatus, Gender } from '../types';
import type { Student, PageResponse } from '../types';
import { studentService } from '../services/studentService';
import dayjs from 'dayjs';

const { Option } = Select;
const { Title } = Typography;

const StudentManagement: React.FC = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<number | undefined>();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [form] = Form.useForm();

  // 统计数据
  const [statistics, setStatistics] = useState({
    total: 0,
    enrolled: 0,
    suspended: 0,
    graduated: 0
  });

  useEffect(() => {
    fetchStudents();
  }, [pagination.current, pagination.pageSize, searchText, selectedStatus]);

  const fetchStudents = async () => {
    setLoading(true);
    try {
      const params = {
        current: pagination.current,
        size: pagination.pageSize,
        name: searchText || undefined,
        studentStatus: selectedStatus
      };

      const response: PageResponse<Student> = await studentService.getStudentPage(params);
      
      setStudents(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      // 更新统计数据
      setStatistics({
        total: response.total,
        enrolled: response.records.filter(s => s.studentStatus === StudentStatus.ENROLLED).length,
        suspended: response.records.filter(s => s.studentStatus === StudentStatus.SUSPENDED).length,
        graduated: response.records.filter(s => s.studentStatus === StudentStatus.GRADUATED).length
      });

    } catch (error) {
      message.error('获取学生列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingStudent(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: Student) => {
    setEditingStudent(record);
    form.setFieldsValue({
      ...record,
      birthDate: record.birthDate ? dayjs(record.birthDate) : null
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await studentService.deleteStudent(id);
      message.success('删除成功');
      fetchStudents();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const studentData = {
        ...values,
        birthDate: values.birthDate ? values.birthDate.format('YYYY-MM-DD') : null
      };

      if (editingStudent) {
        await studentService.updateStudent(editingStudent.id, studentData);
        message.success('更新成功');
      } else {
        await studentService.createStudent(studentData);
        message.success('创建成功');
      }

      setModalVisible(false);
      fetchStudents();
    } catch (error) {
      message.error(editingStudent ? '更新失败' : '创建失败');
    }
  };

  const handleStatusChange = async (id: number, status: number) => {
    try {
      await studentService.updateStudentStatus(id, status);
      message.success('状态更新成功');
      fetchStudents();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case StudentStatus.ENROLLED:
        return 'green';
      case StudentStatus.SUSPENDED:
        return 'orange';
      case StudentStatus.WITHDRAWN:
        return 'red';
      case StudentStatus.GRADUATED:
        return 'blue';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case StudentStatus.ENROLLED:
        return '在读';
      case StudentStatus.SUSPENDED:
        return '休学';
      case StudentStatus.WITHDRAWN:
        return '退学';
      case StudentStatus.GRADUATED:
        return '毕业';
      default:
        return '未知';
    }
  };

  const columns = [
    {
      title: '学号',
      dataIndex: 'studentNo',
      key: 'studentNo',
      width: 120,
      fixed: 'left' as const
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 80,
      render: (gender: number) => gender === Gender.MALE ? '男' : '女'
    },
    {
      title: '专业',
      dataIndex: ['major', 'majorName'],
      key: 'major',
      width: 150
    },
    {
      title: '班级',
      dataIndex: ['class', 'className'],
      key: 'class',
      width: 120
    },
    {
      title: '入学年份',
      dataIndex: 'enrollmentYear',
      key: 'enrollmentYear',
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'studentStatus',
      key: 'studentStatus',
      width: 100,
      render: (status: number, record: Student) => (
        <Select
          value={status}
          size="small"
          style={{ width: 80 }}
          onChange={(value) => handleStatusChange(record.id, value)}
        >
          <Option value={StudentStatus.ENROLLED}>
            <Tag color="green">在读</Tag>
          </Option>
          <Option value={StudentStatus.SUSPENDED}>
            <Tag color="orange">休学</Tag>
          </Option>
          <Option value={StudentStatus.WITHDRAWN}>
            <Tag color="red">退学</Tag>
          </Option>
          <Option value={StudentStatus.GRADUATED}>
            <Tag color="blue">毕业</Tag>
          </Option>
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Student) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个学生吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="学生总数"
              value={statistics.total}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在读学生"
              value={statistics.enrolled}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="休学学生"
              value={statistics.suspended}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="毕业学生"
              value={statistics.graduated}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space>
                <Input
                  placeholder="搜索学生姓名或学号"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                  allowClear
                />
                <Select
                  placeholder="选择状态"
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Option value={StudentStatus.ENROLLED}>在读</Option>
                  <Option value={StudentStatus.SUSPENDED}>休学</Option>
                  <Option value={StudentStatus.WITHDRAWN}>退学</Option>
                  <Option value={StudentStatus.GRADUATED}>毕业</Option>
                </Select>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  新增学生
                </Button>
                <Button icon={<ImportOutlined />}>
                  批量导入
                </Button>
                <Button icon={<ExportOutlined />}>
                  导出数据
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={students}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10
              }));
            }
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingStudent ? '编辑学生' : '新增学生'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="studentNo"
                label="学号"
                rules={[{ required: true, message: '请输入学号' }]}
              >
                <Input placeholder="请输入学号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="gender"
                label="性别"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Select placeholder="请选择性别">
                  <Option value={Gender.MALE}>男</Option>
                  <Option value={Gender.FEMALE}>女</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="birthDate"
                label="出生日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="majorId"
                label="专业"
                rules={[{ required: true, message: '请选择专业' }]}
              >
                <Select placeholder="请选择专业">
                  {/* 这里应该从API获取专业列表 */}
                  <Option value={1}>计算机科学与技术</Option>
                  <Option value={2}>软件工程</Option>
                  <Option value={3}>信息管理与信息系统</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="classId"
                label="班级"
                rules={[{ required: true, message: '请选择班级' }]}
              >
                <Select placeholder="请选择班级">
                  {/* 这里应该从API获取班级列表 */}
                  <Option value={1}>计科2021-1班</Option>
                  <Option value={2}>计科2021-2班</Option>
                  <Option value={3}>软工2021-1班</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enrollmentYear"
                label="入学年份"
                rules={[{ required: true, message: '请输入入学年份' }]}
              >
                <Input type="number" placeholder="请输入入学年份" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="studentStatus"
                label="学籍状态"
                rules={[{ required: true, message: '请选择学籍状态' }]}
              >
                <Select placeholder="请选择学籍状态">
                  <Option value={StudentStatus.ENROLLED}>在读</Option>
                  <Option value={StudentStatus.SUSPENDED}>休学</Option>
                  <Option value={StudentStatus.WITHDRAWN}>退学</Option>
                  <Option value={StudentStatus.GRADUATED}>毕业</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="家庭住址"
          >
            <Input.TextArea rows={3} placeholder="请输入家庭住址" />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingStudent ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StudentManagement;
