{"info": {"name": "University Management System API Tests", "description": "大学管理系统微服务API测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "1. 认证服务测试", "item": [{"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('登录成功', function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.data.token);", "}"]}}]}, {"name": "获取用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('获取用户信息成功', function () {", "    pm.response.to.have.status(200);", "});"]}}]}]}, {"name": "2. 学生管理服务测试", "item": [{"name": "获取学生列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/students/list?page=1&size=10", "host": ["{{baseUrl}}"], "path": ["api", "students", "list"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('获取学生列表成功', function () {", "    pm.response.to.have.status(200);", "});"]}}]}, {"name": "创建学生", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"studentNo\": \"2024001\",\n  \"name\": \"张三\",\n  \"gender\": 1,\n  \"birthDate\": \"2000-01-01\",\n  \"phone\": \"13800138000\",\n  \"email\": \"<EMAIL>\",\n  \"majorId\": 1,\n  \"classId\": 1,\n  \"enrollmentYear\": 2024\n}"}, "url": {"raw": "{{baseUrl}}/api/students", "host": ["{{baseUrl}}"], "path": ["api", "students"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('创建学生成功', function () {", "    pm.response.to.have.status(200);", "});"]}}]}]}, {"name": "3. 教师管理服务测试", "item": [{"name": "获取教师列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/teachers/list", "host": ["{{baseUrl}}"], "path": ["api", "teachers", "list"]}}}, {"name": "创建教师", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"teacherNo\": \"T2024001\",\n  \"name\": \"李老师\",\n  \"gender\": 2,\n  \"phone\": \"13900139000\",\n  \"email\": \"<EMAIL>\",\n  \"departmentId\": 1,\n  \"title\": \"副教授\"\n}"}, "url": {"raw": "{{baseUrl}}/api/teachers", "host": ["{{baseUrl}}"], "path": ["api", "teachers"]}}}]}, {"name": "4. 课程管理服务测试", "item": [{"name": "获取课程列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/courses/list", "host": ["{{baseUrl}}"], "path": ["api", "courses", "list"]}}}, {"name": "创建课程", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"courseCode\": \"CS101\",\n  \"courseName\": \"计算机基础\",\n  \"credits\": 3,\n  \"teacherId\": 1,\n  \"departmentId\": 1,\n  \"semester\": \"2024春\"\n}"}, "url": {"raw": "{{baseUrl}}/api/courses", "host": ["{{baseUrl}}"], "path": ["api", "courses"]}}}]}, {"name": "5. 成绩管理服务测试", "item": [{"name": "获取成绩列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/grades/list", "host": ["{{baseUrl}}"], "path": ["api", "grades", "list"]}}}, {"name": "录入成绩", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"studentId\": 1,\n  \"courseId\": 1,\n  \"score\": 85,\n  \"semester\": \"2024春\",\n  \"examType\": \"期末考试\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grades", "host": ["{{baseUrl}}"], "path": ["api", "grades"]}}}]}, {"name": "6. 财务管理服务测试", "item": [{"name": "获取学费记录", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/finance/tuition/list", "host": ["{{baseUrl}}"], "path": ["api", "finance", "tuition", "list"]}}}]}, {"name": "7. 系统健康检查", "item": [{"name": "API网关健康检查", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/actuator/health", "host": ["{{baseUrl}}"], "path": ["actuator", "health"]}}}, {"name": "服务注册状态检查", "request": {"method": "GET", "url": {"raw": "http://localhost:8848/nacos/v1/ns/instance/list?serviceName=api-gateway", "protocol": "http", "host": ["localhost"], "port": "8848", "path": ["nacos", "v1", "ns", "instance", "list"], "query": [{"key": "serviceName", "value": "api-gateway"}]}}}]}]}