import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Space, Alert } from 'antd';
import { UserOutlined, LockOutlined, BookOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import type { LoginRequest } from '../types';
import './Login.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const { state, login, clearError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  useEffect(() => {
    // 如果已经登录，直接跳转到目标页面
    if (state.isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [state.isAuthenticated, navigate, from]);

  useEffect(() => {
    // 清除之前的错误信息
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleSubmit = async (values: LoginRequest) => {
    try {
      await login(values);
      // 登录成功后会通过useEffect自动跳转
    } catch (error) {
      // 错误已经在AuthContext中处理
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <Space direction="vertical" align="center" size="large">
              <div className="login-logo">
                <BookOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
              </div>
              <div>
                <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                  大学管理系统
                </Title>
                <Text type="secondary">University Management System</Text>
              </div>
            </Space>
          </div>

          {state.error && (
            <Alert
              message={state.error}
              type="error"
              showIcon
              closable
              onClose={clearError}
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={state.isLoading}
                block
                style={{ height: '48px', fontSize: '16px' }}
              >
                {state.isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Space direction="vertical" align="center" size="small">
              <Text type="secondary" style={{ fontSize: '12px' }}>
                支持角色：学生 | 教师 | 管理员
              </Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                演示账号：admin/123456 | teacher/123456 | student/123456
              </Text>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
