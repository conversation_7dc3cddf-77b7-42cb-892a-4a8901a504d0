version: '3.8'

services:
  # 前端开发服务
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api
      - VITE_APP_ENV=development
    command: npm run dev
    networks:
      - university-network

  # 前端生产服务
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    networks:
      - university-network
    profiles:
      - production

networks:
  university-network:
    external: true
