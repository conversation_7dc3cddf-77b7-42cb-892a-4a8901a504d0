package com.university.logistics.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 维修申请实体类
 */
@Data
@TableName("sys_maintenance")
public class Maintenance {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设施名称
     */
    private String facilityName;

    /**
     * 设施位置
     */
    private String location;

    /**
     * 维修类型(0-水电维修,1-家具维修,2-网络维修,3-其他)
     */
    private Integer maintenanceType;

    /**
     * 申请状态(0-待处理,1-处理中,2-已完成,3-已取消)
     */
    private Integer status;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 维修描述
     */
    private String description;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 维修结果
     */
    private String result;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}