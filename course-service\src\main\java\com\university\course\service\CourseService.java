package com.university.course.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.course.entity.Course;

import java.util.List;

/**
 * 课程管理业务逻辑层接口
 */
public interface CourseService extends IService<Course> {

    /**
     * 根据课程编号查询课程信息
     */
    Course getByCourseNo(String courseNo);

    /**
     * 根据学院ID查询课程列表
     */
    List<Course> getByCollegeId(Long collegeId);

    /**
     * 根据专业ID查询课程列表
     */
    List<Course> getByMajorId(Long majorId);

    /**
     * 根据教师ID查询课程列表
     */
    List<Course> getByTeacherId(Long teacherId);

    /**
     * 分页查询课程列表
     */
    Page<Course> getCoursePage(Page<Course> page, Course course);

    /**
     * 更新课程状态
     */
    boolean updateCourseStatus(Long id, Integer status);

    /**
     * 获取课程详细信息
     */
    Course getCourseDetail(Long id);
}