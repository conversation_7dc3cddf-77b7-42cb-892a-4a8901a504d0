package com.university.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.auth.entity.Role;
import com.university.auth.entity.User;
import com.university.auth.mapper.UserMapper;
import com.university.auth.service.UserService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisTemplate;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${jwt.secret:defaultSecretKeyForDevelopmentOnly}")
    private String secretKey;

    @Value("${jwt.expiration:3600000}")
    private long accessTokenExpiration;

    @Value("${jwt.refresh-expiration:86400000}")
    private long refreshTokenExpiration;

    // JWT签名密钥
    private SecretKey getSigningKey() {
        byte[] keyBytes = secretKey.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    @Override
    public User loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户名或密码不正确");
        }
        // 查询用户角色
        user = userMapper.selectUserWithRoles(user.getId());
        if (user.getRoles() != null) {
            user.setAuthorities(user.getRoles().stream()
                    .map(role -> (GrantedAuthority) role)
                    .collect(Collectors.toList()));
        }
        return user;
    }

    @Override
    public String login(String username, String password) {
        // 认证用户
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password)
        );

        // 设置认证信息到上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 生成令牌
        return generateToken(authentication);
    }

    /**
     * 生成JWT令牌
     */
    private String generateToken(Authentication authentication) {
        User user = (User) authentication.getPrincipal();

        // 创建令牌声明
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("username", user.getUsername());
        claims.put("userType", user.getUserType());
        claims.put("roles", user.getRoles().stream()
                .map(Role::getAuthority)
                .collect(Collectors.toList()));

        // 生成访问令牌
        Date now = new Date();
        Date accessTokenExpiresIn = new Date(now.getTime() + accessTokenExpiration);

        String accessToken = Jwts.builder()
                .setClaims(claims)
                .setSubject(user.getUsername())
                .setIssuedAt(now)
                .setExpiration(accessTokenExpiresIn)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();

        // 生成刷新令牌
        Date refreshTokenExpiresIn = new Date(now.getTime() + refreshTokenExpiration);
        String refreshToken = Jwts.builder()
                .setSubject(user.getUsername())
                .setIssuedAt(now)
                .setExpiration(refreshTokenExpiresIn)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();

        // 存储刷新令牌到Redis
        redisTemplate.opsForValue().set(
                "refresh_token:" + user.getUsername(),
                refreshToken,
                refreshTokenExpiration,
                TimeUnit.MILLISECONDS
        );

        // 返回令牌对
        return StrUtil.format("{{\"accessToken\":\"{}\",\"refreshToken\":\"{}\"}}", accessToken, refreshToken);
    }

    @Override
    public String refreshToken(String refreshToken) {
        try {
            // 解析刷新令牌
            Claims claims = Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(refreshToken)
                    .getPayload();

            String username = claims.getSubject();

            // 验证刷新令牌是否有效
            String storedToken = (String) redisTemplate.opsForValue().get("refresh_token:" + username);
            if (StrUtil.isEmpty(storedToken) || !storedToken.equals(refreshToken)) {
                throw new RuntimeException("刷新令牌无效");
            }

            // 加载用户信息
            User user = loadUserByUsername(username);

            // 创建认证信息
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                    user, null, user.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 生成新令牌
            return generateToken(authentication);
        } catch (Exception e) {
            throw new RuntimeException("刷新令牌失败: " + e.getMessage());
        }
    }

    @Override
    public void logout(String token) {
        try {
            // 解析令牌获取用户名
            Claims claims = Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();

            String username = claims.getSubject();

            // 删除Redis中的刷新令牌
            redisTemplate.delete("refresh_token:" + username);

            // 可以添加令牌到黑名单
            redisTemplate.opsForValue().set(
                    "black_token:" + token,
                    "invalid",
                    accessTokenExpiration,
                    TimeUnit.MILLISECONDS
            );
        } catch (Exception e) {
            // 令牌解析失败，可能已过期，直接忽略
        }
    }
}