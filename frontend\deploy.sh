#!/bin/bash

# 大学管理系统前端部署脚本

echo "🚀 开始部署大学管理系统前端..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm环境
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 显示环境信息
echo "📋 环境信息:"
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"

# 安装依赖
echo "📦 安装依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

# 运行类型检查
echo "🔍 运行类型检查..."
npx tsc --noEmit

if [ $? -ne 0 ]; then
    echo "⚠️  类型检查发现问题，但继续构建..."
fi

# 运行ESLint检查
echo "🔍 运行代码检查..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  代码检查发现问题，但继续构建..."
fi

# 构建项目
echo "🏗️  构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

# 检查构建产物
if [ ! -d "dist" ]; then
    echo "❌ 构建产物不存在"
    exit 1
fi

echo "✅ 构建完成！"
echo "📁 构建产物位于 dist/ 目录"

# 可选：启动预览服务器
read -p "🤔 是否启动预览服务器？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌐 启动预览服务器..."
    npm run preview
fi

echo "🎉 部署脚本执行完成！"
