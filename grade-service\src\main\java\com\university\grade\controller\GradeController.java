package com.university.grade.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.grade.entity.Grade;
import com.university.grade.service.GradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 成绩管理控制器
 */
@RestController
@RequestMapping("/grades")
public class GradeController {

    @Autowired
    private GradeService gradeService;

    /**
     * 根据ID查询成绩信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getGradeById(@PathVariable Long id) {
        Grade grade = gradeService.getById(id);
        if (grade == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("成绩记录不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(grade));
    }

    /**
     * 根据学生ID查询成绩列表
     */
    @GetMapping("/student/{studentId}")
    public ResponseEntity<?> getGradesByStudent(@PathVariable Long studentId) {
        List<Grade> grades = gradeService.getByStudentId(studentId);
        return ResponseEntity.ok(createSuccessResponse(grades));
    }

    /**
     * 根据课程ID查询成绩列表
     */
    @GetMapping("/course/{courseId}")
    public ResponseEntity<?> getGradesByCourse(@PathVariable Long courseId) {
        List<Grade> grades = gradeService.getByCourseId(courseId);
        return ResponseEntity.ok(createSuccessResponse(grades));
    }

    /**
     * 根据学生ID和课程ID查询成绩
     */
    @GetMapping("/student/{studentId}/course/{courseId}")
    public ResponseEntity<?> getGradeByStudentAndCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        Grade grade = gradeService.getByStudentIdAndCourseId(studentId, courseId);
        if (grade == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("成绩记录不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(grade));
    }

    /**
     * 分页查询成绩列表
     */
    @GetMapping("/page")
    public ResponseEntity<?> getGradePage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            Grade grade) {

        Page<Grade> page = new Page<>(pageNum, pageSize);
        IPage<Grade> gradePage = gradeService.getGradePage(page, grade);
        return ResponseEntity.ok(createSuccessResponse(gradePage));
    }

    /**
     * 创建成绩
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> createGrade(@RequestBody Grade grade) {
        if (grade.getStudentId() == null || grade.getCourseId() == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("学生ID和课程ID不能为空"));
        }

        // 检查是否已存在该学生该课程的成绩
        Grade existingGrade = gradeService.getByStudentIdAndCourseId(grade.getStudentId(), grade.getCourseId());
        if (existingGrade != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(createErrorResponse("该学生该课程的成绩已存在"));
        }

        boolean saved = gradeService.save(grade);
        if (saved) {
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(createSuccessResponse(grade));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建成绩失败"));
        }
    }

    /**
     * 更新成绩信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> updateGrade(@PathVariable Long id, @RequestBody Grade grade) {
        Grade existingGrade = gradeService.getById(id);
        if (existingGrade == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("成绩记录不存在"));
        }

        grade.setId(id);
        boolean updated = gradeService.updateById(grade);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse(grade));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新成绩失败"));
        }
    }

    /**
     * 更新成绩状态
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateGradeStatus(@PathVariable Long id, @RequestBody Map<String, Object> statusData) {
        Integer status = (Integer) statusData.get("status");
        if (status == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("状态参数不能为空"));
        }

        boolean updated = gradeService.updateGradeStatus(id, status);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse("状态更新成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("状态更新失败"));
        }
    }

    /**
     * 批量导入成绩
     */
    @PostMapping("/import")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> importGrades(@RequestBody List<Grade> gradeList) {
        if (gradeList == null || gradeList.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("成绩列表不能为空"));
        }

        boolean imported = gradeService.importGrades(gradeList);
        if (imported) {
            return ResponseEntity.ok(createSuccessResponse("批量导入成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("批量导入失败"));
        }
    }

    /**
     * 删除成绩
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteGrade(@PathVariable Long id) {
        Grade grade = gradeService.getById(id);
        if (grade == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("成绩记录不存在"));
        }

        boolean deleted = gradeService.removeById(id);
        if (deleted) {
            return ResponseEntity.ok(createSuccessResponse("删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("删除失败"));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("code", 200);
        response.put("message", "操作成功");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}