package com.university.teacher.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 教师实体类
 */
@Data
@TableName("sys_teacher")
public class Teacher {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 教师工号
     */
    private String teacherNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别(0-男,1-女)
     */
    private Integer gender;

    /**
     * 出生日期
     */
    private LocalDateTime birthDate;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 职称(教授/副教授/讲师等)
     */
    private String title;

    /**
     * 所属学院ID
     */
    private Long collegeId;

    /**
     * 所属专业ID
     */
    private Long majorId;

    /**
     * 入职日期
     */
    private LocalDateTime hireDate;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 状态(0-正常,1-禁用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    // @Data注解会自动生成所有getter/setter方法
}