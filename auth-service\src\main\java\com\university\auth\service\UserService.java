package com.university.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.university.auth.entity.User;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User>, UserDetailsService {
    /**
     * 根据用户名加载用户信息（包含角色和权限）
     */
    @Override
    User loadUserByUsername(String username) throws UsernameNotFoundException;

    /**
     * 用户登录认证
     */
    String login(String username, String password);

    /**
     * 刷新令牌
     */
    String refreshToken(String refreshToken);

    /**
     * 登出
     */
    void logout(String token);
}