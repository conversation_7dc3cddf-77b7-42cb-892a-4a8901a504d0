import { api } from './api';
import type { Teacher, ApiResponse, PageResponse, PageRequest } from '../types';

export const teacherService = {
  // 获取教师列表（分页）
  getTeacherPage: async (params: PageRequest & Partial<Teacher>): Promise<PageResponse<Teacher>> => {
    const response = await api.get<ApiResponse<PageResponse<Teacher>>>('/teachers/page', {
      params: {
        pageNum: params.current || 1,
        pageSize: params.size || 10,
        ...params
      }
    });
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取教师列表失败');
    }
  },

  // 根据ID获取教师详情
  getTeacherById: async (id: number): Promise<Teacher> => {
    const response = await api.get<ApiResponse<Teacher>>(`/teachers/${id}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取教师信息失败');
    }
  },

  // 根据教师工号获取教师信息
  getTeacherByNo: async (teacherNo: string): Promise<Teacher> => {
    const response = await api.get<ApiResponse<Teacher>>(`/teachers/teacher-no/${teacherNo}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取教师信息失败');
    }
  },

  // 创建教师
  createTeacher: async (teacher: Omit<Teacher, 'id' | 'createTime' | 'updateTime'>): Promise<Teacher> => {
    const response = await api.post<ApiResponse<Teacher>>('/teachers', teacher);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '创建教师失败');
    }
  },

  // 更新教师信息
  updateTeacher: async (id: number, teacher: Partial<Teacher>): Promise<Teacher> => {
    const response = await api.put<ApiResponse<Teacher>>(`/teachers/${id}`, teacher);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '更新教师信息失败');
    }
  },

  // 删除教师
  deleteTeacher: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/teachers/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除教师失败');
    }
  },

  // 更新教师状态
  updateTeacherStatus: async (id: number, status: number): Promise<void> => {
    const response = await api.patch<ApiResponse<void>>(`/teachers/${id}/status`, { status });
    
    if (!response.success) {
      throw new Error(response.message || '更新教师状态失败');
    }
  },

  // 根据学院ID获取教师列表
  getTeachersByDepartment: async (departmentId: number): Promise<Teacher[]> => {
    const response = await api.get<ApiResponse<Teacher[]>>(`/teachers/department/${departmentId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学院教师列表失败');
    }
  }
};
