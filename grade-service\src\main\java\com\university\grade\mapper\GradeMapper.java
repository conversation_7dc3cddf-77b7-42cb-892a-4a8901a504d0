package com.university.grade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.grade.entity.Grade;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成绩数据访问层接口
 */
public interface GradeMapper extends BaseMapper<Grade> {

    /**
     * 根据学生ID查询成绩列表
     */
    List<Grade> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据课程ID查询成绩列表
     */
    List<Grade> selectByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据学生ID和课程ID查询成绩
     */
    Grade selectByStudentIdAndCourseId(@Param("studentId") Long studentId, @Param("courseId") Long courseId);

    /**
     * 分页查询成绩列表
     */
    IPage<Grade> selectGradePage(IPage<Grade> page, @Param("grade") Grade grade);
}