spring:
  application:
    name: api-gateway
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml
        shared-configs:
          - data-id: common.yml
            refresh: true
    gateway:
      routes:
        # 认证服务路由
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20

        # 学生服务路由
        - id: student-service
          uri: lb://student-service
          predicates:
            - Path=/api/students/**
          filters:
            - StripPrefix=1

        # 教师服务路由
        - id: teacher-service
          uri: lb://teacher-service
          predicates:
            - Path=/api/teachers/**
          filters:
            - StripPrefix=1

        # 课程服务路由
        - id: course-service
          uri: lb://course-service
          predicates:
            - Path=/api/courses/**
          filters:
            - StripPrefix=1

  profiles:
    active: dev

server:
  port: 8080

logging:
  level:
    root: INFO
    org.springframework.cloud.gateway: DEBUG
    com.alibaba.nacos: INFO
    org.springframework.security: INFO