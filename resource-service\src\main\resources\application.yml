spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************
    username: root
    password: 7121020qing
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.university.resource.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 自定义文件上传路径
file:
  upload:
    path: D:/university/upload/resource/

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 服务接口超时配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000

# 日志配置
logging:
  level:
    com.university.resource.mapper: DEBUG
    com.university.resource.service: INFO
    com.university.resource.controller: INFO