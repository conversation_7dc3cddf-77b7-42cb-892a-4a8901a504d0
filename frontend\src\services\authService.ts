import { api } from './api';
import type { LoginRequest, LoginResponse, User, ApiResponse } from '../types';

export const authService = {
  // 用户登录
  login: async (loginData: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<ApiResponse<string>>('/auth/login', loginData);
    
    if (response.success) {
      // 解析返回的JSON字符串
      const tokenData = JSON.parse(response.data);
      return tokenData;
    } else {
      throw new Error(response.message || '登录失败');
    }
  },

  // 刷新token
  refreshToken: async (refreshToken: string): Promise<LoginResponse> => {
    const response = await api.post<ApiResponse<string>>('/auth/refresh-token', {
      refreshToken
    });
    
    if (response.success) {
      const tokenData = JSON.parse(response.data);
      return tokenData;
    } else {
      throw new Error(response.message || '刷新token失败');
    }
  },

  // 用户登出
  logout: async (): Promise<void> => {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 无论请求是否成功，都清除本地存储
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userInfo');
    }
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<ApiResponse<User>>('/auth/current-user');
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取用户信息失败');
    }
  },

  // 检查token是否有效
  validateToken: async (): Promise<boolean> => {
    try {
      await api.get('/auth/validate');
      return true;
    } catch (error) {
      return false;
    }
  },

  // 存储认证信息
  setAuthData: (tokens: LoginResponse, user?: User) => {
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
    if (user) {
      localStorage.setItem('userInfo', JSON.stringify(user));
    }
  },

  // 获取存储的认证信息
  getAuthData: () => {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');
    const userInfo = localStorage.getItem('userInfo');
    
    return {
      accessToken,
      refreshToken,
      userInfo: userInfo ? JSON.parse(userInfo) : null
    };
  },

  // 清除认证信息
  clearAuthData: () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userInfo');
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('accessToken');
    return !!token;
  }
};
