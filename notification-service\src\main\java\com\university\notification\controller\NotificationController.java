package com.university.notification.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.notification.entity.Notification;
import com.university.notification.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知公告管理控制器
 */
@RestController
@RequestMapping("/notifications")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    /**
     * 根据ID查询通知
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getNotificationById(@PathVariable Long id) {
        Notification notification = notificationService.getById(id);
        if (notification == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("通知不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(notification));
    }

    /**
     * 根据接收者ID查询通知列表
     */
    @GetMapping("/recipient/{recipientId}")
    public ResponseEntity<?> getNotificationsByRecipient(@PathVariable Long recipientId) {
        List<Notification> notifications = notificationService.getByRecipientId(recipientId);
        return ResponseEntity.ok(createSuccessResponse(notifications));
    }

    /**
     * 根据通知类型查询通知列表
     */
    @GetMapping("/type/{notificationType}")
    public ResponseEntity<?> getNotificationsByType(@PathVariable Integer notificationType) {
        List<Notification> notifications = notificationService.getByNotificationType(notificationType);
        return ResponseEntity.ok(createSuccessResponse(notifications));
    }

    /**
     * 根据状态查询通知列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<?> getNotificationsByStatus(@PathVariable Integer status) {
        List<Notification> notifications = notificationService.getByStatus(status);
        return ResponseEntity.ok(createSuccessResponse(notifications));
    }

    /**
     * 分页查询通知列表
     */
    @GetMapping("/page")
    public Page<Notification> getPage(@RequestParam(defaultValue = "1") long current,
                                     @RequestParam(defaultValue = "10") long size,
                                     Notification notification) {
        Page<Notification> page = new Page<>(current, size);
        return notificationService.getNotificationPage(page, notification);
    }

    /**
     * 查询未读通知数量
     */
    @GetMapping("/unread/count")
    public Integer getUnreadCount(@RequestParam Long recipientId) {
        return notificationService.getUnreadCount(recipientId);
    }

    /**
     * 创建通知
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public boolean create(@RequestBody Notification notification) {
        notification.setPublishTime(java.time.LocalDateTime.now());
        notification.setCreateTime(java.time.LocalDateTime.now());
        notification.setUpdateTime(java.time.LocalDateTime.now());
        return notificationService.save(notification);
    }

    /**
     * 更新通知
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public boolean update(@RequestBody Notification notification) {
        notification.setUpdateTime(java.time.LocalDateTime.now());
        return notificationService.updateById(notification);
    }

    /**
     * 更新通知状态
     */
    @PutMapping("/status")
    public boolean updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        return notificationService.updateNotificationStatus(id, status);
    }

    /**
     * 批量更新通知状态
     */
    @PutMapping("/batch/status")
    public boolean batchUpdateStatus(@RequestParam List<Long> ids, @RequestParam Integer status) {
        return notificationService.batchUpdateStatus(ids, status);
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/system")
    @PreAuthorize("hasRole('ADMIN')")
    public boolean sendSystemNotification(@RequestBody Notification notification) {
        return notificationService.sendSystemNotification(notification);
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Long id) {
        return notificationService.updateNotificationStatus(id, 2); // 2表示已删除状态
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return response;
    }
}