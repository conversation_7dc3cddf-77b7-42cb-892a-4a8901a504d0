import { api } from './api';
import type { Course, ApiResponse, PageResponse, PageRequest } from '../types';

export const courseService = {
  // 获取课程列表（分页）
  getCoursePage: async (params: PageRequest & Partial<Course>): Promise<PageResponse<Course>> => {
    const response = await api.get<ApiResponse<PageResponse<Course>>>('/courses/page', {
      params: {
        current: params.current || 1,
        size: params.size || 10,
        ...params
      }
    });
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取课程列表失败');
    }
  },

  // 根据ID获取课程详情
  getCourseById: async (id: number): Promise<Course> => {
    const response = await api.get<ApiResponse<Course>>(`/courses/${id}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取课程信息失败');
    }
  },

  // 根据课程编号获取课程信息
  getCourseByCourseNo: async (courseNo: string): Promise<Course> => {
    const response = await api.get<ApiResponse<Course>>(`/courses/courseNo/${courseNo}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取课程信息失败');
    }
  },

  // 创建课程
  createCourse: async (course: Omit<Course, 'id' | 'createTime' | 'updateTime'>): Promise<Course> => {
    const response = await api.post<ApiResponse<Course>>('/courses', course);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '创建课程失败');
    }
  },

  // 更新课程信息
  updateCourse: async (id: number, course: Partial<Course>): Promise<Course> => {
    const response = await api.put<ApiResponse<Course>>(`/courses/${id}`, course);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '更新课程信息失败');
    }
  },

  // 删除课程
  deleteCourse: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/courses/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除课程失败');
    }
  },

  // 更新课程状态
  updateCourseStatus: async (id: number, status: number): Promise<void> => {
    const response = await api.patch<ApiResponse<void>>(`/courses/${id}/status`, { status });
    
    if (!response.success) {
      throw new Error(response.message || '更新课程状态失败');
    }
  },

  // 根据学院ID获取课程列表
  getCoursesByDepartment: async (departmentId: number): Promise<Course[]> => {
    const response = await api.get<ApiResponse<Course[]>>(`/courses/department/${departmentId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学院课程列表失败');
    }
  },

  // 根据教师ID获取课程列表
  getCoursesByTeacher: async (teacherId: number): Promise<Course[]> => {
    const response = await api.get<ApiResponse<Course[]>>(`/courses/teacher/${teacherId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取教师课程列表失败');
    }
  }
};
