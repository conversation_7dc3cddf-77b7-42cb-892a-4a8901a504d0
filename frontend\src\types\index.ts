// 用户相关类型定义
export interface User {
  id: number;
  username: string;
  realName?: string;
  email?: string;
  phone?: string;
  status: number;
  userType: number; // 1-学生 2-教师 3-管理员
  createTime?: string;
  updateTime?: string;
  roles?: Role[];
}

export interface Role {
  id: number;
  roleName: string;
  roleCode: string;
  description?: string;
  status: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
}

// 学生相关类型
export interface Student {
  id: number;
  userId: number;
  studentNo: string;
  name: string;
  gender: number; // 1-男 2-女
  birthDate?: string;
  idCard?: string;
  phone?: string;
  email?: string;
  address?: string;
  majorId: number;
  classId: number;
  enrollmentYear: number;
  graduationYear?: number;
  studentStatus: number; // 1-在读 2-休学 3-退学 4-毕业
  createTime?: string;
  updateTime?: string;
  // 关联信息
  major?: Major;
  class?: Class;
  department?: Department;
}

// 教师相关类型
export interface Teacher {
  id: number;
  userId: number;
  teacherNo: string;
  name: string;
  gender: number;
  birthDate?: string;
  phone?: string;
  email?: string;
  departmentId: number;
  title?: string; // 职称
  degree?: string; // 学位
  specialty?: string; // 专业特长
  status: number;
  createTime?: string;
  updateTime?: string;
  // 关联信息
  department?: Department;
}

// 课程相关类型
export interface Course {
  id: number;
  courseNo: string;
  courseName: string;
  credits: number;
  hours: number;
  courseType: number; // 1-必修 2-选修
  departmentId: number;
  teacherId?: number;
  semester: string;
  maxStudents?: number;
  currentStudents?: number;
  description?: string;
  status: number;
  createTime?: string;
  updateTime?: string;
  // 关联信息
  teacher?: Teacher;
  department?: Department;
}

// 基础数据类型
export interface Department {
  id: number;
  deptName: string;
  deptCode: string;
  description?: string;
  status: number;
}

export interface Major {
  id: number;
  majorName: string;
  majorCode: string;
  departmentId: number;
  degreeType: number; // 1-本科 2-硕士 3-博士
  duration: number;
  description?: string;
  status: number;
  department?: Department;
}

export interface Class {
  id: number;
  className: string;
  classCode: string;
  majorId: number;
  enrollmentYear: number;
  advisorId?: number;
  maxStudents: number;
  currentStudents: number;
  status: number;
  major?: Major;
  advisor?: Teacher;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data: T;
}

export interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 分页请求参数
export interface PageRequest {
  current?: number;
  size?: number;
}

// 用户类型枚举
export enum UserType {
  STUDENT = 1,
  TEACHER = 2,
  ADMIN = 3
}

// 性别枚举
export enum Gender {
  MALE = 1,
  FEMALE = 2
}

// 学生状态枚举
export enum StudentStatus {
  ENROLLED = 1,
  SUSPENDED = 2,
  WITHDRAWN = 3,
  GRADUATED = 4
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
  roles?: UserType[]; // 允许访问的角色
}
