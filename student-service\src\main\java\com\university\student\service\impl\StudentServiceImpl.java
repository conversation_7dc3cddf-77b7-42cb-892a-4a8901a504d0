package com.university.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.student.entity.Student;
import com.university.student.mapper.StudentMapper;
import com.university.student.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 学生服务实现类
 */
@Service
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements StudentService {

    @Autowired
    private StudentMapper studentMapper;

    @Override
    public Student getByStudentNo(String studentNo) {
        return studentMapper.selectByStudentNo(studentNo);
    }

    @Override
    public List<Student> getByCollegeId(Long collegeId) {
        return studentMapper.selectByCollegeId(collegeId);
    }

    @Override
    public List<Student> getByMajorId(Long majorId) {
        return studentMapper.selectByMajorId(majorId);
    }

    @Override
    public List<Student> getByClassId(Long classId) {
        return studentMapper.selectByClassId(classId);
    }

    @Override
    public Page<Student> getStudentPage(Page<Student> page, Student student) {
        IPage<Student> studentIPage = studentMapper.selectStudentPage(page, student);
        return (Page<Student>) studentIPage;
    }

    @Override
    @Transactional
    public boolean updateStudentStatus(Long id, String status) {
        Student student = new Student();
        student.setId(id);
        student.setStudentStatus(status);
        return updateById(student);
    }

    @Override
    @Transactional
    public boolean importStudents(List<Student> studentList) {
        if (studentList == null || studentList.isEmpty()) {
            return false;
        }
        // 批量插入，使用MyBatis-Plus的saveBatch方法
        return saveBatch(studentList);
    }

    @Override
    public Student getStudentDetail(Long id) {
        // 查询学生基本信息
        Student student = getById(id);
        if (student == null) {
            return null;
        }
        // 这里可以添加关联查询，获取班级、专业、学院等详细信息
        // 实际项目中可能需要调用其他服务或关联查询
        return student;
    }
}