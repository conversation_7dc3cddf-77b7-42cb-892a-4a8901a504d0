package com.university.resource.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 资源实体类
 */
@Data
@TableName("sys_resource")
public class Resource {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源类型(0-文档,1-视频,2-音频,3-图片,4-其他)
     */
    private Integer resourceType;

    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 文件大小(KB)
     */
    private Long fileSize;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 访问URL
     */
    private String accessUrl;

    /**
     * 上传者ID
     */
    private Long uploaderId;

    /**
     * 所属课程ID
     */
    private Long courseId;

    /**
     * 资源状态(0-未审核,1-已审核,2-已下架)
     */
    private Integer status;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}