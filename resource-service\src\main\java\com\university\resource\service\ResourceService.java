package com.university.resource.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.resource.entity.Resource;

import java.util.List;

/**
 * 资源管理业务逻辑层接口
 */
public interface ResourceService extends IService<Resource> {

    /**
     * 根据课程ID查询资源列表
     */
    List<Resource> getByCourseId(Long courseId);

    /**
     * 根据上传者ID查询资源列表
     */
    List<Resource> getByUploaderId(Long uploaderId);

    /**
     * 根据资源类型查询资源列表
     */
    List<Resource> getByResourceType(Integer resourceType);

    /**
     * 分页查询资源列表
     */
    Page<Resource> getResourcePage(Page<Resource> page, Resource resource);

    /**
     * 更新资源状态
     */
    boolean updateResourceStatus(Long id, Integer status);

    /**
     * 增加下载次数
     */
    boolean incrementDownloadCount(Long id);
}