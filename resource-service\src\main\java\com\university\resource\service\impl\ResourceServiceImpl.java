package com.university.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.resource.entity.Resource;
import com.university.resource.mapper.ResourceMapper;
import com.university.resource.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资源管理业务逻辑实现类
 */
@Service
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, Resource> implements ResourceService {

    @Autowired
    private ResourceMapper resourceMapper;

    @Override
    public List<Resource> getByCourseId(Long courseId) {
        QueryWrapper<Resource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId);
        queryWrapper.eq("status", 1); // 只查询已审核的资源
        queryWrapper.orderByDesc("create_time");
        return resourceMapper.selectList(queryWrapper);
    }

    @Override
    public List<Resource> getByUploaderId(Long uploaderId) {
        QueryWrapper<Resource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uploader_id", uploaderId);
        queryWrapper.orderByDesc("create_time");
        return resourceMapper.selectList(queryWrapper);
    }

    @Override
    public List<Resource> getByResourceType(Integer resourceType) {
        QueryWrapper<Resource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("resource_type", resourceType);
        queryWrapper.eq("status", 1); // 只查询已审核的资源
        queryWrapper.orderByDesc("download_count");
        return resourceMapper.selectList(queryWrapper);
    }

    @Override
    public Page<Resource> getResourcePage(Page<Resource> page, Resource resource) {
        QueryWrapper<Resource> queryWrapper = new QueryWrapper<>(resource);
        queryWrapper.orderByDesc("create_time");
        return (Page<Resource>) resourceMapper.selectResourcePage(page, resource);
    }

    @Override
    public boolean updateResourceStatus(Long id, Integer status) {
        Resource resource = new Resource();
        resource.setId(id);
        resource.setStatus(status);
        return updateById(resource);
    }

    @Override
    public boolean incrementDownloadCount(Long id) {
        UpdateWrapper<Resource> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.setSql("download_count = download_count + 1");
        return resourceMapper.update(null, updateWrapper) > 0;
    }
}