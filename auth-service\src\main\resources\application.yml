spring:
  main:
    allow-bean-definition-overriding: true
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: 7121020qing
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 2000

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.university.auth.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Spring Security配置
security:
  password:
    encoder:
      secret: university_auth_encoder_secret
      iterations: 10

# JWT配置
jwt:
  token:
    header: Authorization
    prefix: Bearer 
    expiration: 86400000
    refresh-expiration: 604800000
    secret: ${jwt.secret}

# 验证码配置
captcha:
  cache:
    key-prefix: "captcha:"
    expire-in: 300
  length: 4
  width: 130
  height: 48
  font-size: 24

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 服务接口超时配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000

# 日志配置
logging:
  level:
    com.university.auth.mapper: DEBUG
    com.university.auth.service: INFO
    com.university.auth.controller: INFO
    org.springframework.security: INFO
    com.university.auth.security: DEBUG