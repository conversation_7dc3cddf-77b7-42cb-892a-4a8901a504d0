// API相关常量
export const API_BASE_URL = 'http://localhost:8080/api';

// 本地存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_INFO: 'userInfo',
  THEME: 'theme',
  LANGUAGE: 'language'
} as const;

// 用户类型
export const USER_TYPES = {
  STUDENT: 1,
  TEACHER: 2,
  ADMIN: 3
} as const;

// 性别
export const GENDERS = {
  MALE: 1,
  FEMALE: 2
} as const;

// 学生状态
export const STUDENT_STATUS = {
  ENROLLED: 1,    // 在读
  SUSPENDED: 2,   // 休学
  WITHDRAWN: 3,   // 退学
  GRADUATED: 4    // 毕业
} as const;

// 课程类型
export const COURSE_TYPES = {
  REQUIRED: 1,    // 必修
  ELECTIVE: 2     // 选修
} as const;

// 学位类型
export const DEGREE_TYPES = {
  BACHELOR: 1,    // 本科
  MASTER: 2,      // 硕士
  DOCTOR: 3       // 博士
} as const;

// 状态映射
export const STATUS_MAP = {
  ACTIVE: 1,      // 激活/正常
  INACTIVE: 0     // 停用/禁用
} as const;

// 分页默认配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
  SHOW_TOTAL: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
} as const;

// 表格配置
export const TABLE_CONFIG = {
  SCROLL_X: 1200,
  ROW_KEY: 'id',
  SIZE: 'middle' as const
} as const;

// 表单配置
export const FORM_CONFIG = {
  LAYOUT: 'vertical' as const,
  LABEL_COL: { span: 24 },
  WRAPPER_COL: { span: 24 }
} as const;

// 上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
  ACCEPTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ACCEPTED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
} as const;

// 日期格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY'
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ID_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  STUDENT_NO: /^[0-9]{8,12}$/,
  TEACHER_NO: /^[0-9]{6,10}$/
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络',
  SERVER_ERROR: '服务器内部错误',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '数据验证失败',
  UPLOAD_ERROR: '文件上传失败'
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '已安全退出',
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  UPLOAD_SUCCESS: '上传成功',
  COPY_SUCCESS: '复制成功'
} as const;

// 主题配置
export const THEME_CONFIG = {
  PRIMARY_COLOR: '#1890ff',
  SUCCESS_COLOR: '#52c41a',
  WARNING_COLOR: '#faad14',
  ERROR_COLOR: '#f5222d',
  INFO_COLOR: '#1890ff',
  BORDER_RADIUS: '6px',
  BOX_SHADOW: '0 2px 8px rgba(0, 0, 0, 0.06)'
} as const;

// 动画配置
export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out'
  }
} as const;

// 路由路径
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  STUDENTS: '/students',
  TEACHERS: '/teachers',
  COURSES: '/courses',
  GRADES: '/grades',
  FINANCE: '/finance',
  RESOURCES: '/resources',
  NOTIFICATIONS: '/notifications',
  ANALYTICS: '/analytics',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  MY_COURSES: '/my-courses',
  MY_GRADES: '/my-grades',
  MY_FINANCE: '/my-finance',
  MY_TEACHING: '/my-teaching'
} as const;

// 菜单权限配置
export const MENU_PERMISSIONS = {
  [ROUTES.DASHBOARD]: [USER_TYPES.STUDENT, USER_TYPES.TEACHER, USER_TYPES.ADMIN],
  [ROUTES.STUDENTS]: [USER_TYPES.ADMIN, USER_TYPES.TEACHER],
  [ROUTES.TEACHERS]: [USER_TYPES.ADMIN],
  [ROUTES.COURSES]: [USER_TYPES.ADMIN, USER_TYPES.TEACHER],
  [ROUTES.GRADES]: [USER_TYPES.ADMIN, USER_TYPES.TEACHER],
  [ROUTES.FINANCE]: [USER_TYPES.ADMIN],
  [ROUTES.RESOURCES]: [USER_TYPES.ADMIN],
  [ROUTES.NOTIFICATIONS]: [USER_TYPES.ADMIN],
  [ROUTES.ANALYTICS]: [USER_TYPES.ADMIN],
  [ROUTES.MY_COURSES]: [USER_TYPES.STUDENT],
  [ROUTES.MY_GRADES]: [USER_TYPES.STUDENT],
  [ROUTES.MY_FINANCE]: [USER_TYPES.STUDENT],
  [ROUTES.MY_TEACHING]: [USER_TYPES.TEACHER],
  [ROUTES.PROFILE]: [USER_TYPES.STUDENT, USER_TYPES.TEACHER, USER_TYPES.ADMIN],
  [ROUTES.SETTINGS]: [USER_TYPES.ADMIN]
} as const;

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const;

// 请求超时时间
export const REQUEST_TIMEOUT = 10000; // 10秒

// 文件类型图标映射
export const FILE_TYPE_ICONS = {
  pdf: 'file-pdf',
  doc: 'file-word',
  docx: 'file-word',
  xls: 'file-excel',
  xlsx: 'file-excel',
  ppt: 'file-ppt',
  pptx: 'file-ppt',
  txt: 'file-text',
  jpg: 'file-image',
  jpeg: 'file-image',
  png: 'file-image',
  gif: 'file-image',
  zip: 'file-zip',
  rar: 'file-zip'
} as const;
