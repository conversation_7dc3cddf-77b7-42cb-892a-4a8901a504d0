package com.university.grade.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.grade.entity.Grade;
import com.university.grade.mapper.GradeMapper;
import com.university.grade.service.GradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 成绩管理业务逻辑实现类
 */
@Service
public class GradeServiceImpl extends ServiceImpl<GradeMapper, Grade> implements GradeService {

    @Autowired
    private GradeMapper gradeMapper;

    @Override
    public List<Grade> getByStudentId(Long studentId) {
        return gradeMapper.selectByStudentId(studentId);
    }

    @Override
    public List<Grade> getByCourseId(Long courseId) {
        return gradeMapper.selectByCourseId(courseId);
    }

    @Override
    public Grade getByStudentIdAndCourseId(Long studentId, Long courseId) {
        return gradeMapper.selectByStudentIdAndCourseId(studentId, courseId);
    }

    @Override
    public Page<Grade> getGradePage(Page<Grade> page, Grade grade) {
        return gradeMapper.selectGradePage(page, grade);
    }

    @Override
    public boolean updateGradeStatus(Long id, Integer status) {
        Grade grade = new Grade();
        grade.setId(id);
        grade.setStatus(status);
        return updateById(grade);
    }

    @Override
    @Transactional
    public boolean importGrades(List<Grade> gradeList) {
        // 批量保存或更新成绩信息
        return saveOrUpdateBatch(gradeList);
    }
}