# 大学管理系统前端

基于 React + TypeScript + Vite + Ant Design 构建的现代化大学管理系统前端界面。

## 功能特性

### 🎯 核心功能
- **用户认证**: JWT令牌认证，支持学生/教师/管理员多角色登录
- **权限控制**: 基于角色的访问控制(RBAC)，不同角色显示不同功能
- **响应式设计**: 适配桌面端和移动端，提供良好的用户体验
- **现代化UI**: 使用Ant Design组件库，界面美观易用

### 📊 管理模块
- **仪表板**: 数据统计、最近活动、快速操作
- **学生管理**: 学生信息CRUD、状态管理、批量操作
- **教师管理**: 教师档案管理、工作量统计
- **课程管理**: 课程信息、教学计划、选课系统
- **成绩管理**: 成绩录入、统计分析、报表生成
- **财务管理**: 学费管理、奖学金管理
- **系统管理**: 用户管理、权限配置

### 🔧 技术栈
- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite 7
- **UI组件库**: Ant Design 5
- **路由管理**: React Router 6
- **状态管理**: Context API + useReducer
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **图标库**: Ant Design Icons

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 演示账号

- **管理员**: admin / 123456
- **教师**: teacher / 123456
- **学生**: student / 123456

## API接口

前端通过API网关与后端微服务通信：
- **基础URL**: http://localhost:8080/api
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON

### 主要接口
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `GET /students/page` - 分页获取学生列表
- `POST /students` - 创建学生
- `PUT /students/{id}` - 更新学生信息
- `DELETE /students/{id}` - 删除学生

## 项目结构

```
src/
├── components/          # 通用组件
│   ├── Layout/         # 布局组件
│   └── ProtectedRoute.tsx  # 路由保护组件
├── contexts/           # React Context
│   └── AuthContext.tsx # 认证上下文
├── pages/              # 页面组件
│   ├── Login.tsx       # 登录页面
│   ├── Dashboard.tsx   # 仪表板
│   ├── StudentManagement.tsx  # 学生管理
│   ├── TeacherManagement.tsx  # 教师管理
│   ├── CourseManagement.tsx   # 课程管理
│   ├── Profile.tsx     # 个人信息
│   └── NotFound.tsx    # 404页面
├── services/           # API服务
│   ├── api.ts          # HTTP客户端配置
│   ├── authService.ts  # 认证服务
│   ├── studentService.ts   # 学生服务
│   ├── teacherService.ts   # 教师服务
│   ├── courseService.ts    # 课程服务
│   └── mockService.ts      # Mock数据服务
├── hooks/              # 自定义Hooks
│   └── useTable.ts     # 表格状态管理
├── utils/              # 工具函数
│   └── index.ts        # 通用工具
├── constants/          # 常量定义
│   └── index.ts        # 应用常量
├── types/              # TypeScript类型定义
│   └── index.ts        # 通用类型
├── App.tsx             # 主应用组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 用户角色与权限

### 管理员 (Admin)
- 完整的系统管理权限
- 学生、教师、课程、成绩、财务管理
- 系统配置和用户管理

### 教师 (Teacher)
- 课程管理和成绩录入
- 学生信息查看
- 教学相关功能

### 学生 (Student)
- 个人信息管理
- 课程选择和成绩查看
- 费用查询

## 开发指南

### 添加新页面
1. 在 `src/pages/` 目录创建页面组件
2. 在 `src/App.tsx` 中添加路由配置
3. 根据需要添加权限控制

### 添加新服务
1. 在 `src/services/` 目录创建服务文件
2. 定义API接口方法
3. 在组件中使用服务

### 样式定制
- 全局样式在 `src/index.css`
- 组件样式使用CSS Modules或styled-components
- 主题配置在 `src/App.tsx` 的 ConfigProvider

## 注意事项

1. 确保后端服务已启动并运行在正确端口
2. 检查API网关配置是否正确
3. 开发时注意跨域问题的处理
4. 生产环境需要配置正确的API地址

## 相关文档

- [开发指南](./DEVELOPMENT_GUIDE.md) - 详细的开发规范和指南
- [功能说明](./FEATURES.md) - 完整的功能列表和说明
- [项目总结](./PROJECT_SUMMARY.md) - 项目架构和技术总结
