package com.university.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;

import java.time.LocalDateTime;

/**
 * 角色实体类
 */
@Data
@TableName("sys_role")
public class Role implements GrantedAuthority {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String roleName; // 角色名称
    private String roleCode; // 角色编码
    private String description; // 角色描述
    private Integer status; // 0-禁用 1-正常
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    @Override
    public String getAuthority() {
        return roleCode;
    }

    // @Data注解会自动生成getter/setter方法
}