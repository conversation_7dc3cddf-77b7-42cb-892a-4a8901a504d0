import React from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Progress, List, Avatar, Tag } from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  BookOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  ClockCircleOutlined,
  NotificationOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { UserType } from '../types';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { state, hasRole } = useAuth();

  // 模拟数据
  const statisticsData = {
    totalStudents: 1250,
    totalTeachers: 85,
    totalCourses: 156,
    activeUsers: 892,
    studentGrowth: 12.5,
    teacherGrowth: 8.3,
    courseGrowth: 15.2,
    userGrowth: 9.8
  };

  const recentActivities = [
    {
      id: 1,
      type: 'course',
      title: '新增课程：高等数学A',
      description: '张教授创建了新的数学课程',
      time: '2小时前',
      avatar: '张'
    },
    {
      id: 2,
      type: 'student',
      title: '学生注册',
      description: '5名新学生完成了注册',
      time: '4小时前',
      avatar: '学'
    },
    {
      id: 3,
      type: 'grade',
      title: '成绩录入',
      description: '计算机科学导论课程成绩已录入',
      time: '6小时前',
      avatar: '成'
    },
    {
      id: 4,
      type: 'notification',
      title: '系统通知',
      description: '期末考试安排已发布',
      time: '1天前',
      avatar: '通'
    }
  ];

  const upcomingEvents = [
    {
      id: 1,
      title: '期末考试',
      date: '2024-01-15',
      type: 'exam',
      status: 'upcoming'
    },
    {
      id: 2,
      title: '学期总结会议',
      date: '2024-01-20',
      type: 'meeting',
      status: 'upcoming'
    },
    {
      id: 3,
      title: '寒假开始',
      date: '2024-01-25',
      type: 'holiday',
      status: 'upcoming'
    }
  ];

  const renderStatisticCard = (title: string, value: number, icon: React.ReactNode, growth: number, color: string) => (
    <Card>
      <Statistic
        title={title}
        value={value}
        prefix={icon}
        valueStyle={{ color }}
        suffix={
          <span style={{ fontSize: '14px', marginLeft: '8px' }}>
            {growth > 0 ? <RiseOutlined style={{ color: '#52c41a' }} /> : <FallOutlined style={{ color: '#ff4d4f' }} />}
            <span style={{ color: growth > 0 ? '#52c41a' : '#ff4d4f', marginLeft: '4px' }}>
              {Math.abs(growth)}%
            </span>
          </span>
        }
      />
    </Card>
  );

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = '早上好';
    if (hour >= 12 && hour < 18) {
      greeting = '下午好';
    } else if (hour >= 18) {
      greeting = '晚上好';
    }

    const roleText = hasRole(UserType.ADMIN) ? '管理员' : 
                    hasRole(UserType.TEACHER) ? '老师' : '同学';

    return `${greeting}，${state.user?.realName || state.user?.username}${roleText}！`;
  };

  return (
    <div style={{ padding: '0' }}>
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: '24px', background: 'linear-gradient(135deg, #1890ff, #36cfc9)' }}>
        <div style={{ color: 'white' }}>
          <Title level={2} style={{ color: 'white', margin: 0 }}>
            {getWelcomeMessage()}
          </Title>
          <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '16px' }}>
            欢迎使用大学管理系统，今天是 {new Date().toLocaleDateString('zh-CN', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              weekday: 'long'
            })}
          </Text>
        </div>
      </Card>

      {/* 统计卡片 - 仅管理员可见 */}
      {hasRole(UserType.ADMIN) && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            {renderStatisticCard('学生总数', statisticsData.totalStudents, <TeamOutlined />, statisticsData.studentGrowth, '#1890ff')}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatisticCard('教师总数', statisticsData.totalTeachers, <UserOutlined />, statisticsData.teacherGrowth, '#52c41a')}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatisticCard('课程总数', statisticsData.totalCourses, <BookOutlined />, statisticsData.courseGrowth, '#faad14')}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatisticCard('活跃用户', statisticsData.activeUsers, <TrophyOutlined />, statisticsData.userGrowth, '#722ed1')}
          </Col>
        </Row>
      )}

      <Row gutter={[16, 16]}>
        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ClockCircleOutlined />
                最近活动
              </Space>
            }
            extra={<a href="#">查看全部</a>}
          >
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar style={{ backgroundColor: '#1890ff' }}>
                        {item.avatar}
                      </Avatar>
                    }
                    title={item.title}
                    description={
                      <Space direction="vertical" size={0}>
                        <Text type="secondary">{item.description}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.time}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 即将到来的事件 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <NotificationOutlined />
                即将到来
              </Space>
            }
            extra={<a href="#">查看日历</a>}
          >
            <List
              itemLayout="horizontal"
              dataSource={upcomingEvents}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        {item.title}
                        <Tag color={item.type === 'exam' ? 'red' : item.type === 'meeting' ? 'blue' : 'green'}>
                          {item.type === 'exam' ? '考试' : item.type === 'meeting' ? '会议' : '假期'}
                        </Tag>
                      </Space>
                    }
                    description={item.date}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 - 根据角色显示不同内容 */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card title="快速操作">
            <Row gutter={[16, 16]}>
              {hasRole(UserType.ADMIN) && (
                <>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <TeamOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
                      <div>学生管理</div>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <UserOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                      <div>教师管理</div>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <BookOutlined style={{ fontSize: '24px', color: '#faad14', marginBottom: '8px' }} />
                      <div>课程管理</div>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <TrophyOutlined style={{ fontSize: '24px', color: '#722ed1', marginBottom: '8px' }} />
                      <div>成绩管理</div>
                    </Card>
                  </Col>
                </>
              )}
              
              {hasRole(UserType.TEACHER) && (
                <>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <BookOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
                      <div>我的课程</div>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <TrophyOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                      <div>成绩录入</div>
                    </Card>
                  </Col>
                </>
              )}
              
              {hasRole(UserType.STUDENT) && (
                <>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <BookOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
                      <div>选课系统</div>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <Card size="small" hoverable style={{ textAlign: 'center' }}>
                      <TrophyOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                      <div>我的成绩</div>
                    </Card>
                  </Col>
                </>
              )}
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
