import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Row,
  Col,
  Statistic,
  DatePicker,
  Switch,
  Badge,
  Avatar,
  List,
  Typography,
  Divider
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  BellOutlined,
  NotificationOutlined,
  UserOutlined,
  CalendarOutlined,
  SendOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { TextArea } = Input;
const { Text, Title } = Typography;

interface Notification {
  id: number;
  title: string;
  content: string;
  type: string;
  priority: string;
  status: string;
  publishTime: string;
  author: string;
  readCount: number;
  targetAudience: string;
  isTop: boolean;
}

const NotificationManagement: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingNotification, setEditingNotification] = useState<Notification | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [form] = Form.useForm();

  // 模拟数据
  const mockNotifications: Notification[] = [
    {
      id: 1,
      title: '关于2024年春季学期期末考试安排的通知',
      content: '各位同学：2024年春季学期期末考试将于1月15日-1月25日举行，请各位同学做好复习准备...',
      type: '考试通知',
      priority: '高',
      status: '已发布',
      publishTime: '2024-01-10 09:00:00',
      author: '教务处',
      readCount: 1250,
      targetAudience: '全体学生',
      isTop: true
    },
    {
      id: 2,
      title: '学生宿舍管理规定更新通知',
      content: '为了更好地维护宿舍秩序，现对学生宿舍管理规定进行更新，具体内容如下...',
      type: '管理通知',
      priority: '中',
      status: '已发布',
      publishTime: '2024-01-08 14:30:00',
      author: '学生处',
      readCount: 856,
      targetAudience: '住宿学生',
      isTop: false
    },
    {
      id: 3,
      title: '2024年寒假放假时间安排',
      content: '根据学校校历安排，2024年寒假放假时间为1月25日-2月25日，请各位师生合理安排假期时间...',
      type: '校务通知',
      priority: '高',
      status: '已发布',
      publishTime: '2024-01-05 10:15:00',
      author: '校办',
      readCount: 2100,
      targetAudience: '全体师生',
      isTop: true
    },
    {
      id: 4,
      title: '图书馆开放时间调整通知',
      content: '因系统维护需要，图书馆开放时间临时调整为每日8:00-20:00，给大家带来不便敬请谅解...',
      type: '服务通知',
      priority: '中',
      status: '已发布',
      publishTime: '2024-01-03 16:45:00',
      author: '图书馆',
      readCount: 432,
      targetAudience: '全体师生',
      isTop: false
    },
    {
      id: 5,
      title: '新学期选课系统开放通知',
      content: '2024年春季学期选课系统将于2月26日上午9:00正式开放，请同学们及时登录选课...',
      type: '教学通知',
      priority: '高',
      status: '草稿',
      publishTime: '2024-01-02 11:20:00',
      author: '教务处',
      readCount: 0,
      targetAudience: '全体学生',
      isTop: false
    }
  ];

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setNotifications(mockNotifications);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取通知数据失败');
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingNotification(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (record: Notification) => {
    setEditingNotification(record);
    setIsModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      onOk: () => {
        setNotifications(notifications.filter(notification => notification.id !== id));
        message.success('删除成功');
      }
    });
  };

  const handlePublish = (id: number) => {
    setNotifications(notifications.map(notification => 
      notification.id === id 
        ? { ...notification, status: '已发布', publishTime: new Date().toLocaleString() }
        : notification
    ));
    message.success('发布成功');
  };

  const handleTop = (id: number, isTop: boolean) => {
    setNotifications(notifications.map(notification => 
      notification.id === id 
        ? { ...notification, isTop: !isTop }
        : notification
    ));
    message.success(isTop ? '取消置顶成功' : '置顶成功');
  };

  const columns: ColumnsType<Notification> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 300,
      render: (title: string, record: Notification) => (
        <div>
          <Space>
            {record.isTop && <Tag color="red">置顶</Tag>}
            <Text strong={record.isTop}>{title}</Text>
          </Space>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => {
        const color = {
          '考试通知': 'red',
          '教学通知': 'blue',
          '管理通知': 'green',
          '校务通知': 'purple',
          '服务通知': 'orange'
        }[type] || 'default';
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => {
        const color = priority === '高' ? 'red' : priority === '中' ? 'orange' : 'green';
        return <Tag color={color}>{priority}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge 
          status={status === '已发布' ? 'success' : 'default'} 
          text={status} 
        />
      ),
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      key: 'publishTime',
      width: 150,
    },
    {
      title: '发布者',
      dataIndex: 'author',
      key: 'author',
      width: 100,
    },
    {
      title: '阅读量',
      dataIndex: 'readCount',
      key: 'readCount',
      width: 80,
      render: (count: number) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{count}</span>
      ),
    },
    {
      title: '目标受众',
      dataIndex: 'targetAudience',
      key: 'targetAudience',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleEdit(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === '草稿' && (
            <Button
              type="link"
              size="small"
              icon={<SendOutlined />}
              onClick={() => handlePublish(record.id)}
            >
              发布
            </Button>
          )}
          <Button
            type="link"
            size="small"
            onClick={() => handleTop(record.id, record.isTop)}
          >
            {record.isTop ? '取消置顶' : '置顶'}
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 过滤数据
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = !searchText || 
      notification.title.toLowerCase().includes(searchText.toLowerCase()) ||
      notification.content.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = !selectedType || notification.type === selectedType;
    const matchesStatus = !selectedStatus || notification.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // 统计数据
  const totalNotifications = filteredNotifications.length;
  const publishedCount = filteredNotifications.filter(n => n.status === '已发布').length;
  const draftCount = filteredNotifications.filter(n => n.status === '草稿').length;
  const totalReads = filteredNotifications.reduce((sum, n) => sum + n.readCount, 0);

  return (
    <div style={{ padding: '24px' }}>
      <Card title="通知公告管理" style={{ marginBottom: '24px' }}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="通知总数"
                value={totalNotifications}
                prefix={<NotificationOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已发布"
                value={publishedCount}
                prefix={<BellOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="草稿"
                value={draftCount}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总阅读量"
                value={totalReads}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和操作栏 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Input
              placeholder="搜索通知标题或内容"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择类型"
              style={{ width: '100%' }}
              value={selectedType}
              onChange={setSelectedType}
              allowClear
            >
              <Option value="考试通知">考试通知</Option>
              <Option value="教学通知">教学通知</Option>
              <Option value="管理通知">管理通知</Option>
              <Option value="校务通知">校务通知</Option>
              <Option value="服务通知">服务通知</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              style={{ width: '100%' }}
              value={selectedStatus}
              onChange={setSelectedStatus}
              allowClear
            >
              <Option value="已发布">已发布</Option>
              <Option value="草稿">草稿</Option>
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                发布通知
              </Button>
              <Button icon={<BellOutlined />}>
                批量发布
              </Button>
              <Button icon={<DeleteOutlined />}>
                批量删除
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 通知表格 */}
        <Table
          columns={columns}
          dataSource={filteredNotifications}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredNotifications.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default NotificationManagement;
