package com.university.student.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.university.student.entity.Student;

import java.util.List;

/**
 * 学生服务接口
 */
public interface StudentService extends IService<Student> {
    /**
     * 根据学号查询学生
     */
    Student getByStudentNo(String studentNo);

    /**
     * 根据学院ID查询学生
     */
    List<Student> getByCollegeId(Long collegeId);

    /**
     * 根据专业ID查询学生
     */
    List<Student> getByMajorId(Long majorId);

    /**
     * 根据班级ID查询学生
     */
    List<Student> getByClassId(Long classId);

    /**
     * 分页查询学生列表
     */
    Page<Student> getStudentPage(Page<Student> page, Student student);

    /**
     * 更新学生状态
     */
    boolean updateStudentStatus(Long id, String status);

    /**
     * 批量导入学生信息
     */
    boolean importStudents(List<Student> studentList);

    /**
     * 获取学生详细信息（包含班级、专业、学院信息）
     */
    Student getStudentDetail(Long id);
}