import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  message,
  Tabs,
  Row,
  Col,
  Divider,
  Typography,
  Space,
  Tag
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  LockOutlined,
  SettingOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { UserType } from '../types';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Profile: React.FC = () => {
  const { state } = useAuth();
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      setLoading(false);
      message.success('头像上传成功');
    }
  };

  const handleProfileUpdate = async (values: any) => {
    try {
      setLoading(true);
      // 这里应该调用API更新用户信息
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      message.success('个人信息更新成功');
      setEditMode(false);
    } catch (error) {
      message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (values: any) => {
    try {
      setLoading(true);
      // 这里应该调用API修改密码
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      message.success('密码修改成功');
      passwordForm.resetFields();
    } catch (error) {
      message.error('密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  const getUserTypeText = (userType: number) => {
    switch (userType) {
      case UserType.STUDENT:
        return { text: '学生', color: 'blue' };
      case UserType.TEACHER:
        return { text: '教师', color: 'green' };
      case UserType.ADMIN:
        return { text: '管理员', color: 'red' };
      default:
        return { text: '用户', color: 'default' };
    }
  };

  const userTypeInfo = getUserTypeText(state.user?.userType || 0);

  const uploadButton = (
    <div>
      <CameraOutlined />
      <div style={{ marginTop: 8 }}>上传头像</div>
    </div>
  );

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={24}>
        <Col span={8}>
          {/* 用户基本信息卡片 */}
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Upload
                name="avatar"
                listType="picture-card"
                className="avatar-uploader"
                showUploadList={false}
                action="/api/upload/avatar"
                beforeUpload={(file) => {
                  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                  if (!isJpgOrPng) {
                    message.error('只能上传 JPG/PNG 格式的图片!');
                  }
                  const isLt2M = file.size / 1024 / 1024 < 2;
                  if (!isLt2M) {
                    message.error('图片大小不能超过 2MB!');
                  }
                  return isJpgOrPng && isLt2M;
                }}
                onChange={handleAvatarChange}
              >
                {state.user?.avatar ? (
                  <Avatar size={100} src={state.user.avatar} />
                ) : (
                  <Avatar size={100} icon={<UserOutlined />} />
                )}
                <div className="upload-overlay">
                  <CameraOutlined />
                </div>
              </Upload>
              
              <Title level={3} style={{ marginTop: 16, marginBottom: 8 }}>
                {state.user?.realName || state.user?.username}
              </Title>
              
              <Tag color={userTypeInfo.color} style={{ marginBottom: 16 }}>
                {userTypeInfo.text}
              </Tag>
              
              <div style={{ color: '#666' }}>
                <div>用户名: {state.user?.username}</div>
                <div>邮箱: {state.user?.email || '未设置'}</div>
                <div>手机: {state.user?.phone || '未设置'}</div>
              </div>
            </div>
          </Card>

          {/* 账户统计 */}
          <Card title="账户统计" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {Math.floor(Math.random() * 100)}
                  </div>
                  <div style={{ color: '#666' }}>登录次数</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {Math.floor(Math.random() * 30)}
                  </div>
                  <div style={{ color: '#666' }}>活跃天数</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={16}>
          <Card>
            <Tabs defaultActiveKey="1">
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    个人信息
                  </span>
                }
                key="1"
              >
                <div style={{ marginBottom: 16 }}>
                  <Space>
                    <Title level={4}>个人信息</Title>
                    <Button
                      type={editMode ? 'default' : 'primary'}
                      icon={editMode ? <SaveOutlined /> : <EditOutlined />}
                      onClick={() => {
                        if (editMode) {
                          form.submit();
                        } else {
                          setEditMode(true);
                          form.setFieldsValue(state.user);
                        }
                      }}
                    >
                      {editMode ? '保存' : '编辑'}
                    </Button>
                    {editMode && (
                      <Button onClick={() => setEditMode(false)}>
                        取消
                      </Button>
                    )}
                  </Space>
                </div>

                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleProfileUpdate}
                  initialValues={state.user || {}}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="realName"
                        label="真实姓名"
                      >
                        <Input
                          placeholder="请输入真实姓名"
                          disabled={!editMode}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                      >
                        <Input
                          placeholder="用户名"
                          disabled
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input
                          placeholder="请输入邮箱"
                          disabled={!editMode}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号"
                        rules={[
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                        ]}
                      >
                        <Input
                          placeholder="请输入手机号"
                          disabled={!editMode}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    name="userType"
                    label="用户类型"
                  >
                    <Input
                      value={userTypeInfo.text}
                      disabled
                    />
                  </Form.Item>
                </Form>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <LockOutlined />
                    修改密码
                  </span>
                }
                key="2"
              >
                <Title level={4}>修改密码</Title>
                <Form
                  form={passwordForm}
                  layout="vertical"
                  onFinish={handlePasswordChange}
                  style={{ maxWidth: 400 }}
                >
                  <Form.Item
                    name="currentPassword"
                    label="当前密码"
                    rules={[
                      { required: true, message: '请输入当前密码' }
                    ]}
                  >
                    <Input.Password placeholder="请输入当前密码" />
                  </Form.Item>

                  <Form.Item
                    name="newPassword"
                    label="新密码"
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 6, message: '密码至少6个字符' }
                    ]}
                  >
                    <Input.Password placeholder="请输入新密码" />
                  </Form.Item>

                  <Form.Item
                    name="confirmPassword"
                    label="确认新密码"
                    dependencies={['newPassword']}
                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('newPassword') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'));
                        },
                      }),
                    ]}
                  >
                    <Input.Password placeholder="请再次输入新密码" />
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                    >
                      修改密码
                    </Button>
                  </Form.Item>
                </Form>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <SettingOutlined />
                    系统设置
                  </span>
                }
                key="3"
              >
                <Title level={4}>系统设置</Title>
                <div style={{ color: '#666' }}>
                  <p>• 主题设置</p>
                  <p>• 语言设置</p>
                  <p>• 通知设置</p>
                  <p>• 隐私设置</p>
                  <Divider />
                  <Text type="secondary">更多设置功能正在开发中...</Text>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>

      <style jsx>{`
        .avatar-uploader {
          position: relative;
        }
        .upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
          color: white;
          font-size: 20px;
          border-radius: 50%;
        }
        .avatar-uploader:hover .upload-overlay {
          opacity: 1;
        }
      `}</style>
    </div>
  );
};

export default Profile;
