package com.university.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.resource.entity.Resource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源数据访问层接口
 */
public interface ResourceMapper extends BaseMapper<Resource> {

    /**
     * 根据课程ID查询资源列表
     */
    List<Resource> selectByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据上传者ID查询资源列表
     */
    List<Resource> selectByUploaderId(@Param("uploaderId") Long uploaderId);

    /**
     * 根据资源类型查询资源列表
     */
    List<Resource> selectByResourceType(@Param("resourceType") Integer resourceType);

    /**
     * 分页查询资源列表
     */
    IPage<Resource> selectResourcePage(IPage<Resource> page, @Param("resource") Resource resource);
}