package com.university.notification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.notification.entity.Notification;
import com.university.notification.mapper.NotificationMapper;
import com.university.notification.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知公告管理业务逻辑实现类
 */
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> implements NotificationService {

    @Autowired
    private NotificationMapper notificationMapper;

    @Override
    public List<Notification> getByRecipientId(Long recipientId) {
        QueryWrapper<Notification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("recipient_id", recipientId)
                    .ne("status", 2) // 排除已删除状态
                    .orderByDesc("is_top", "publish_time");
        return notificationMapper.selectList(queryWrapper);
    }

    @Override
    public List<Notification> getByNotificationType(Integer notificationType) {
        QueryWrapper<Notification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("notification_type", notificationType)
                    .ne("status", 2)
                    .orderByDesc("publish_time");
        return notificationMapper.selectList(queryWrapper);
    }

    @Override
    public List<Notification> getByStatus(Integer status) {
        QueryWrapper<Notification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                    .orderByDesc("publish_time");
        return notificationMapper.selectList(queryWrapper);
    }

    @Override
    public Page<Notification> getNotificationPage(Page<Notification> page, Notification notification) {
        QueryWrapper<Notification> queryWrapper = new QueryWrapper<>(notification);
        queryWrapper.ne("status", 2)
                    .orderByDesc("is_top", "publish_time");
        return (Page<Notification>) notificationMapper.selectNotificationPage(page, queryWrapper);
    }

    @Override
    public boolean updateNotificationStatus(Long id, Integer status) {
        Notification notification = new Notification();
        notification.setId(id);
        notification.setStatus(status);
        return updateById(notification);
    }

    @Override
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        QueryWrapper<Notification> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        Notification notification = new Notification();
        notification.setStatus(status);
        return update(notification, queryWrapper);
    }

    @Override
    public Integer getUnreadCount(Long recipientId) {
        return notificationMapper.selectUnreadCount(recipientId, 0); // 0表示未读状态
    }

    @Override
    public boolean sendSystemNotification(Notification notification) {
        notification.setNotificationType(0); // 系统通知类型
        notification.setStatus(0); // 未读状态
        notification.setIsTop(0); // 默认不置顶
        notification.setPublishTime(LocalDateTime.now());
        notification.setCreateTime(LocalDateTime.now());
        notification.setUpdateTime(LocalDateTime.now());
        return save(notification);
    }
}