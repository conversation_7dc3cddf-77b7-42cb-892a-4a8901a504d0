import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  InputNumber,
  message,
  Row,
  Col,
  Statistic,
  DatePicker,
  Upload,
  Divider
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  FileExcelOutlined,
  TrophyOutlined,
  BookOutlined,
  TeamOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface Grade {
  id: number;
  studentId: string;
  studentName: string;
  courseId: string;
  courseName: string;
  semester: string;
  examType: string;
  score: number;
  grade: string;
  gpa: number;
  examDate: string;
  teacherName: string;
  status: string;
}

const GradeManagement: React.FC = () => {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingGrade, setEditingGrade] = useState<Grade | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedSemester, setSelectedSemester] = useState<string>('');
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [form] = Form.useForm();

  // 模拟数据
  const mockGrades: Grade[] = [
    {
      id: 1,
      studentId: '2024001001',
      studentName: '张三',
      courseId: 'CS101',
      courseName: '计算机科学导论',
      semester: '2024-1',
      examType: '期末考试',
      score: 85,
      grade: 'B',
      gpa: 3.0,
      examDate: '2024-01-15',
      teacherName: '李教授',
      status: '已发布'
    },
    {
      id: 2,
      studentId: '2024001002',
      studentName: '李四',
      courseId: 'CS101',
      courseName: '计算机科学导论',
      semester: '2024-1',
      examType: '期末考试',
      score: 92,
      grade: 'A',
      gpa: 4.0,
      examDate: '2024-01-15',
      teacherName: '李教授',
      status: '已发布'
    },
    {
      id: 3,
      studentId: '2024001003',
      studentName: '王五',
      courseId: 'MATH101',
      courseName: '高等数学',
      semester: '2024-1',
      examType: '期末考试',
      score: 78,
      grade: 'C+',
      gpa: 2.3,
      examDate: '2024-01-18',
      teacherName: '王老师',
      status: '已发布'
    },
    {
      id: 4,
      studentId: '2024001001',
      studentName: '张三',
      courseId: 'MATH101',
      courseName: '高等数学',
      semester: '2024-1',
      examType: '期中考试',
      score: 88,
      grade: 'B+',
      gpa: 3.3,
      examDate: '2023-11-20',
      teacherName: '王老师',
      status: '已发布'
    }
  ];

  useEffect(() => {
    fetchGrades();
  }, []);

  const fetchGrades = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setGrades(mockGrades);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取成绩数据失败');
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingGrade(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (record: Grade) => {
    setEditingGrade(record);
    setIsModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条成绩记录吗？',
      onOk: () => {
        setGrades(grades.filter(grade => grade.id !== id));
        message.success('删除成功');
      }
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingGrade) {
        // 编辑
        setGrades(grades.map(grade => 
          grade.id === editingGrade.id ? { ...grade, ...values } : grade
        ));
        message.success('修改成功');
      } else {
        // 新增
        const newGrade: Grade = {
          id: Date.now(),
          ...values,
          grade: getGradeByScore(values.score),
          gpa: getGPAByScore(values.score),
          status: '已发布'
        };
        setGrades([...grades, newGrade]);
        message.success('添加成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const getGradeByScore = (score: number): string => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  const getGPAByScore = (score: number): number => {
    if (score >= 90) return 4.0;
    if (score >= 80) return 3.0;
    if (score >= 70) return 2.0;
    if (score >= 60) return 1.0;
    return 0.0;
  };

  const columns: ColumnsType<Grade> = [
    {
      title: '学号',
      dataIndex: 'studentId',
      key: 'studentId',
      width: 120,
    },
    {
      title: '学生姓名',
      dataIndex: 'studentName',
      key: 'studentName',
      width: 100,
    },
    {
      title: '课程编号',
      dataIndex: 'courseId',
      key: 'courseId',
      width: 100,
    },
    {
      title: '课程名称',
      dataIndex: 'courseName',
      key: 'courseName',
      width: 150,
    },
    {
      title: '学期',
      dataIndex: 'semester',
      key: 'semester',
      width: 100,
    },
    {
      title: '考试类型',
      dataIndex: 'examType',
      key: 'examType',
      width: 100,
    },
    {
      title: '分数',
      dataIndex: 'score',
      key: 'score',
      width: 80,
      render: (score: number) => (
        <span style={{ 
          color: score >= 90 ? '#52c41a' : score >= 80 ? '#1890ff' : score >= 60 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      ),
    },
    {
      title: '等级',
      dataIndex: 'grade',
      key: 'grade',
      width: 80,
      render: (grade: string) => {
        const color = grade === 'A' ? 'green' : grade === 'B' ? 'blue' : grade === 'C' ? 'orange' : 'red';
        return <Tag color={color}>{grade}</Tag>;
      },
    },
    {
      title: 'GPA',
      dataIndex: 'gpa',
      key: 'gpa',
      width: 80,
    },
    {
      title: '考试日期',
      dataIndex: 'examDate',
      key: 'examDate',
      width: 120,
    },
    {
      title: '任课教师',
      dataIndex: 'teacherName',
      key: 'teacherName',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === '已发布' ? 'green' : 'orange'}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 过滤数据
  const filteredGrades = grades.filter(grade => {
    const matchesSearch = !searchText || 
      grade.studentName.toLowerCase().includes(searchText.toLowerCase()) ||
      grade.studentId.includes(searchText) ||
      grade.courseName.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesSemester = !selectedSemester || grade.semester === selectedSemester;
    const matchesCourse = !selectedCourse || grade.courseId === selectedCourse;
    
    return matchesSearch && matchesSemester && matchesCourse;
  });

  // 统计数据
  const totalGrades = filteredGrades.length;
  const averageScore = totalGrades > 0 ? 
    Math.round(filteredGrades.reduce((sum, grade) => sum + grade.score, 0) / totalGrades * 10) / 10 : 0;
  const passRate = totalGrades > 0 ? 
    Math.round(filteredGrades.filter(grade => grade.score >= 60).length / totalGrades * 100 * 10) / 10 : 0;
  const excellentRate = totalGrades > 0 ? 
    Math.round(filteredGrades.filter(grade => grade.score >= 90).length / totalGrades * 100 * 10) / 10 : 0;

  return (
    <div style={{ padding: '24px' }}>
      <Card title="成绩管理" style={{ marginBottom: '24px' }}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="成绩总数"
                value={totalGrades}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均分"
                value={averageScore}
                precision={1}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="及格率"
                value={passRate}
                precision={1}
                suffix="%"
                prefix={<BookOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="优秀率"
                value={excellentRate}
                precision={1}
                suffix="%"
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和操作栏 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Input
              placeholder="搜索学生姓名、学号或课程"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择学期"
              style={{ width: '100%' }}
              value={selectedSemester}
              onChange={setSelectedSemester}
              allowClear
            >
              <Option value="2024-1">2024-1</Option>
              <Option value="2023-2">2023-2</Option>
              <Option value="2023-1">2023-1</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择课程"
              style={{ width: '100%' }}
              value={selectedCourse}
              onChange={setSelectedCourse}
              allowClear
            >
              <Option value="CS101">CS101</Option>
              <Option value="MATH101">MATH101</Option>
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                录入成绩
              </Button>
              <Button icon={<UploadOutlined />}>
                批量导入
              </Button>
              <Button icon={<DownloadOutlined />}>
                导出数据
              </Button>
              <Button icon={<FileExcelOutlined />}>
                成绩单模板
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 成绩表格 */}
        <Table
          columns={columns}
          dataSource={filteredGrades}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredGrades.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 成绩录入/编辑对话框 */}
      <Modal
        title={editingGrade ? '编辑成绩' : '录入成绩'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            examType: '期末考试',
            status: '已发布'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="studentId"
                label="学号"
                rules={[{ required: true, message: '请输入学号' }]}
              >
                <Input placeholder="请输入学号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="studentName"
                label="学生姓名"
                rules={[{ required: true, message: '请输入学生姓名' }]}
              >
                <Input placeholder="请输入学生姓名" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="courseId"
                label="课程编号"
                rules={[{ required: true, message: '请输入课程编号' }]}
              >
                <Input placeholder="请输入课程编号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="courseName"
                label="课程名称"
                rules={[{ required: true, message: '请输入课程名称' }]}
              >
                <Input placeholder="请输入课程名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="semester"
                label="学期"
                rules={[{ required: true, message: '请选择学期' }]}
              >
                <Select placeholder="请选择学期">
                  <Option value="2024-1">2024-1</Option>
                  <Option value="2023-2">2023-2</Option>
                  <Option value="2023-1">2023-1</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="examType"
                label="考试类型"
                rules={[{ required: true, message: '请选择考试类型' }]}
              >
                <Select placeholder="请选择考试类型">
                  <Option value="期末考试">期末考试</Option>
                  <Option value="期中考试">期中考试</Option>
                  <Option value="平时考试">平时考试</Option>
                  <Option value="补考">补考</Option>
                  <Option value="重修">重修</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="score"
                label="分数"
                rules={[
                  { required: true, message: '请输入分数' },
                  { type: 'number', min: 0, max: 100, message: '分数必须在0-100之间' }
                ]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={1}
                  style={{ width: '100%' }}
                  placeholder="请输入分数"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="teacherName"
                label="任课教师"
                rules={[{ required: true, message: '请输入任课教师' }]}
              >
                <Input placeholder="请输入任课教师" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="examDate"
                label="考试日期"
                rules={[{ required: true, message: '请选择考试日期' }]}
              >
                <DatePicker style={{ width: '100%' }} placeholder="请选择考试日期" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="已发布">已发布</Option>
                  <Option value="待发布">待发布</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default GradeManagement;
