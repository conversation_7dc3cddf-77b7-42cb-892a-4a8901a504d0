package com.university.course.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.course.entity.Course;
import com.university.course.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程管理控制器
 */
@RestController
@RequestMapping("/courses")
public class CourseController {

    @Autowired
    private CourseService courseService;

    /**
     * 根据ID查询课程信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getCourseById(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("课程不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(course));
    }

    /**
     * 根据课程编号查询课程信息
     */
    @GetMapping("/courseNo/{courseNo}")
    public ResponseEntity<?> getCourseByCourseNo(@PathVariable String courseNo) {
        Course course = courseService.getByCourseNo(courseNo);
        if (course == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("课程不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(course));
    }

    /**
     * 根据学院ID查询课程列表
     */
    @GetMapping("/department/{departmentId}")
    public ResponseEntity<?> getCoursesByDepartment(@PathVariable Long departmentId) {
        List<Course> courses = courseService.getByCollegeId(departmentId);
        return ResponseEntity.ok(createSuccessResponse(courses));
    }

    /**
     * 根据教师ID查询课程列表
     */
    @GetMapping("/teacher/{teacherId}")
    public ResponseEntity<?> getCoursesByTeacher(@PathVariable Long teacherId) {
        List<Course> courses = courseService.getByTeacherId(teacherId);
        return ResponseEntity.ok(createSuccessResponse(courses));
    }

    /**
     * 分页查询课程列表
     */
    @GetMapping("/page")
    public ResponseEntity<?> getCoursePage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            Course course) {

        Page<Course> page = new Page<>(pageNum, pageSize);
        IPage<Course> coursePage = courseService.getCoursePage(page, course);
        return ResponseEntity.ok(createSuccessResponse(coursePage));
    }

    /**
     * 创建课程
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> createCourse(@RequestBody Course course) {
        if (StrUtil.isEmpty(course.getCourseNo())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("课程编号不能为空"));
        }

        // 检查课程编号是否已存在
        if (courseService.getByCourseNo(course.getCourseNo()) != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(createErrorResponse("课程编号已存在"));
        }

        boolean saved = courseService.save(course);
        if (saved) {
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(createSuccessResponse(course));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建课程失败"));
        }
    }

    /**
     * 更新课程信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> updateCourse(@PathVariable Long id, @RequestBody Course course) {
        Course existingCourse = courseService.getById(id);
        if (existingCourse == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("课程不存在"));
        }

        course.setId(id);
        boolean updated = courseService.updateById(course);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse(course));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新课程失败"));
        }
    }

    /**
     * 更新课程状态
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateCourseStatus(@PathVariable Long id, @RequestBody Map<String, Object> statusData) {
        Integer status = (Integer) statusData.get("status");
        if (status == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("状态参数不能为空"));
        }

        boolean updated = courseService.updateCourseStatus(id, status);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse("状态更新成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("状态更新失败"));
        }
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteCourse(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("课程不存在"));
        }

        boolean deleted = courseService.removeById(id);
        if (deleted) {
            return ResponseEntity.ok(createSuccessResponse("删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("删除失败"));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("code", 200);
        response.put("message", "操作成功");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}