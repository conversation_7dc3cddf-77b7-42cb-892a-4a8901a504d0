spring:
  application:
    name: auth-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        file-extension: yml
        group: DEFAULT_GROUP
        prefix: ${spring.application.name}
  profiles:
    active: dev

server:
  port: 8081

# 日志配置
logging:
  level:
    root: INFO
    com.university.auth: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'