package com.university.logistics.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.logistics.entity.Maintenance;
import com.university.logistics.service.MaintenanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 后勤维修管理控制器
 */
@RestController
@RequestMapping("/maintenance")
public class MaintenanceController {

    @Autowired
    private MaintenanceService maintenanceService;

    /**
     * 根据ID查询维修申请
     */
    @GetMapping("/{id}")
    public Maintenance getById(@PathVariable Long id) {
        return maintenanceService.getById(id);
    }

    /**
     * 根据申请人ID查询维修申请列表
     */
    @GetMapping("/applicant/{applicantId}")
    public List<Maintenance> getByApplicantId(@PathVariable Long applicantId) {
        return maintenanceService.getByApplicantId(applicantId);
    }

    /**
     * 根据状态查询维修申请列表
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LOGISTICS')")
    public List<Maintenance> getByStatus(@PathVariable Integer status) {
        return maintenanceService.getByStatus(status);
    }

    /**
     * 分页查询维修申请列表
     */
    @GetMapping("/page")
    public Page<Maintenance> getPage(@RequestParam(defaultValue = "1") long current,
                                     @RequestParam(defaultValue = "10") long size,
                                     Maintenance maintenance) {
        Page<Maintenance> page = new Page<>(current, size);
        return maintenanceService.getMaintenancePage(page, maintenance);
    }

    /**
     * 创建维修申请
     */
    @PostMapping
    public boolean create(@RequestBody Maintenance maintenance) {
        return maintenanceService.save(maintenance);
    }

    /**
     * 更新维修申请
     */
    @PutMapping
    public boolean update(@RequestBody Maintenance maintenance) {
        return maintenanceService.updateById(maintenance);
    }

    /**
     * 更新维修申请状态
     */
    @PutMapping("/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LOGISTICS')")
    public boolean updateStatus(@RequestParam Long id, @RequestParam Integer status, @RequestParam(required = false) Long handlerId) {
        return maintenanceService.updateMaintenanceStatus(id, status, handlerId);
    }

    /**
     * 分配维修任务
     */
    @PutMapping("/assign")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LOGISTICS')")
    public boolean assignTask(@RequestParam Long id, @RequestParam Long handlerId) {
        return maintenanceService.assignMaintenanceTask(id, handlerId);
    }

    /**
     * 删除维修申请
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @maintenanceService.getById(#id).getApplicantId() == authentication.principal.id")
    public boolean delete(@PathVariable Long id) {
        return maintenanceService.removeById(id);
    }
}