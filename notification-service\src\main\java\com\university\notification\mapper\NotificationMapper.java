package com.university.notification.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.university.notification.entity.Notification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知公告数据访问层接口
 */
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 根据接收者ID查询通知列表
     */
    List<Notification> selectByRecipientId(Long recipientId);

    /**
     * 根据通知类型查询通知列表
     */
    List<Notification> selectByNotificationType(Integer notificationType);

    /**
     * 根据状态查询通知列表
     */
    List<Notification> selectByStatus(Integer status);

    /**
     * 分页查询通知列表
     */
    IPage<Notification> selectNotificationPage(IPage<Notification> page, @Param(Constants.WRAPPER) Wrapper<Notification> queryWrapper);

    /**
     * 根据接收者ID和状态查询未读通知数量
     */
    Integer selectUnreadCount(@Param("recipientId") Long recipientId, @Param("status") Integer status);
}