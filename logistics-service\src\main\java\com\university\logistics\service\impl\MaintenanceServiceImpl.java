package com.university.logistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.logistics.entity.Maintenance;
import com.university.logistics.mapper.MaintenanceMapper;
import com.university.logistics.service.MaintenanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 维修申请管理业务逻辑实现类
 */
@Service
public class MaintenanceServiceImpl extends ServiceImpl<MaintenanceMapper, Maintenance> implements MaintenanceService {

    @Autowired
    private MaintenanceMapper maintenanceMapper;

    @Override
    public List<Maintenance> getByApplicantId(Long applicantId) {
        QueryWrapper<Maintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("applicant_id", applicantId);
        queryWrapper.orderByDesc("create_time");
        return maintenanceMapper.selectList(queryWrapper);
    }

    @Override
    public List<Maintenance> getByStatus(Integer status) {
        QueryWrapper<Maintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        queryWrapper.orderByAsc("create_time");
        return maintenanceMapper.selectList(queryWrapper);
    }

    @Override
    public List<Maintenance> getByMaintenanceType(Integer maintenanceType) {
        QueryWrapper<Maintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("maintenance_type", maintenanceType);
        queryWrapper.orderByDesc("create_time");
        return maintenanceMapper.selectList(queryWrapper);
    }

    @Override
    public Page<Maintenance> getMaintenancePage(Page<Maintenance> page, Maintenance maintenance) {
        return maintenanceMapper.selectMaintenancePage(page, maintenance);
    }

    @Override
    public boolean updateMaintenanceStatus(Long id, Integer status, Long handlerId) {
        Maintenance maintenance = new Maintenance();
        maintenance.setId(id);
        maintenance.setStatus(status);
        maintenance.setHandlerId(handlerId);

        // 根据状态设置相应时间
        if (status == 1) { // 处理中
            maintenance.setHandleTime(LocalDateTime.now());
        } else if (status == 2) { // 已完成
            maintenance.setFinishTime(LocalDateTime.now());
        }

        return updateById(maintenance);
    }

    @Override
    public boolean assignMaintenanceTask(Long id, Long handlerId) {
        Maintenance maintenance = new Maintenance();
        maintenance.setId(id);
        maintenance.setHandlerId(handlerId);
        maintenance.setStatus(1); // 状态改为处理中
        maintenance.setHandleTime(LocalDateTime.now());
        return updateById(maintenance);
    }
}