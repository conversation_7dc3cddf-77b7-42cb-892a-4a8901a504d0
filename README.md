# 大学管理系统 - Spring Cloud微服务架构

## 项目概述
基于Spring Cloud框架开发的大学管理系统，采用微服务架构实现各功能模块解耦，支持高可用和可扩展性。

## 功能模块

### 1. 用户认证与授权服务
- 统一身份认证（学生/教师/管理员多角色）
- 基于RBAC模型的权限管理
- 单点登录（SSO）集成

### 2. 学生管理服务
- 学籍信息管理（入学/毕业/转学流程）
- 个人档案维护
- 奖惩记录管理

### 3. 教师管理服务
- 教师档案管理
- 工作量统计
- 绩效考核

### 4. 课程教学服务
- 课程信息管理
- 教学计划制定
- 排课管理
- 选课系统

### 5. 成绩管理服务
- 成绩录入与审核
- 绩点计算与排名
- 成绩分析报表
- 补考/重修管理

### 6. 财务管理服务
- 学费收缴
- 奖学金/助学金管理
- 财务流水查询
- 票据管理

### 7. 资源管理服务
- 图书馆系统
- 实验室预约
- 教室资源调度

### 8. 后勤服务
- 宿舍管理
- 校园卡服务
- 校园设施报修

### 9. 通知公告服务
- 校园新闻发布
- 个人消息推送
- 日程提醒

### 10. 数据分析服务
- 教学质量评估
- 学生行为分析
- 招生就业统计

## 技术架构

### 核心组件
- 服务注册发现：Eureka/Nacos
- API网关：Spring Cloud Gateway
- 配置中心：Spring Cloud Config
- 服务熔断：Resilience4j
- 分布式事务：Seata
- 链路追踪：Sleuth+Zipkin

## 项目结构
```
springcloud2/
├── README.md
├── pom.xml                  # 父工程依赖管理
├── api-gateway/             # API网关服务
├── auth-service/            # 用户认证与授权服务
├── student-service/         # 学生管理服务
├── teacher-service/         # 教师管理服务
├── course-service/          # 课程教学服务
├── score-service/           # 成绩管理服务
├── finance-service/         # 财务管理服务
├── resource-service/        # 资源管理服务
├── logistics-service/       # 后勤服务
├── notification-service/    # 通知公告服务
├── analysis-service/        # 数据分析服务
└── common/                  # 公共组件模块
```

## 环境要求
- JDK 11+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Nacos/Eureka Server

## 启动说明
1. 启动服务注册中心（Nacos/Eureka）
2. 启动配置中心
3. 依次启动各微服务模块
4. 启动API网关

## 开发指南
详细开发文档和接口说明请参见各模块下的README.md