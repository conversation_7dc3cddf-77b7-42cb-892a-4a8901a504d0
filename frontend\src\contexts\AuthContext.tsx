import React, { createContext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import type { User, LoginRequest, UserType } from '../types';
import { authService } from '../services/authService';
import { message } from 'antd';

// 认证状态类型
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 认证动作类型
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'CLEAR_ERROR' };

// 认证上下文类型
interface AuthContextType {
  state: AuthState;
  login: (loginData: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  hasRole: (role: UserType) => boolean;
  hasAnyRole: (roles: UserType[]) => boolean;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// 认证reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// 创建上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 初始化时检查本地存储的认证信息
  useEffect(() => {
    const initAuth = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        const { accessToken, userInfo } = authService.getAuthData();
        
        if (accessToken && userInfo) {
          // 暂时跳过token验证，直接设置用户信息
          dispatch({ type: 'SET_USER', payload: userInfo });

          // TODO: 后续启动后端服务后再启用token验证
          // const isValid = await authService.validateToken();
          // if (isValid) {
          //   dispatch({ type: 'SET_USER', payload: userInfo });
          // } else {
          //   authService.clearAuthData();
          // }
        }
      } catch (error) {
        console.error('初始化认证失败:', error);
        authService.clearAuthData();
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initAuth();
  }, []);

  // 登录方法
  const login = useCallback(async (loginData: LoginRequest) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const tokens = await authService.login(loginData);

      // 存储token
      authService.setAuthData(tokens);

      // 获取用户信息
      const user = await authService.getCurrentUser();

      // 更新本地存储的用户信息
      authService.setAuthData(tokens, user);

      dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      message.success('登录成功');
    } catch (error: any) {
      const errorMessage = error.message || '登录失败';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      message.error(errorMessage);
      throw error;
    }
  }, []);

  // 登出方法
  const logout = useCallback(async () => {
    try {
      await authService.logout();
      dispatch({ type: 'LOGOUT' });
      message.success('已安全退出');
    } catch (error) {
      console.error('登出失败:', error);
      // 即使登出请求失败，也要清除本地状态
      dispatch({ type: 'LOGOUT' });
    }
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // 检查用户是否有指定角色
  const hasRole = (role: UserType): boolean => {
    return state.user?.userType === role;
  };

  // 检查用户是否有任意一个指定角色
  const hasAnyRole = (roles: UserType[]): boolean => {
    return state.user ? roles.includes(state.user.userType) : false;
  };

  const value: AuthContextType = {
    state,
    login,
    logout,
    clearError,
    hasRole,
    hasAnyRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
