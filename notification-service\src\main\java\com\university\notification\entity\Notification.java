package com.university.notification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 通知公告实体类
 */
@Data
@TableName("sys_notification")
public class Notification {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者名称
     */
    private String senderName;

    /**
     * 接收者ID(0表示全体用户)
     */
    private Long recipientId;

    /**
     * 接收者类型(0-全体用户,1-学生,2-教师,3-管理员)
     */
    private Integer recipientType;

    /**
     * 通知类型(0-系统通知,1-课程通知,2-校园公告,3-个人消息)
     */
    private Integer notificationType;

    /**
     * 通知状态(0-未读,1-已读,2-已删除)
     */
    private Integer status;

    /**
     * 是否置顶(0-否,1-是)
     */
    private Integer isTop;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}