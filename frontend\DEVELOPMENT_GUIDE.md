# 大学管理系统前端开发指南

## 🚀 快速开始

### 环境要求
- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+
- Git 2.0+

### 安装与启动
```bash
# 克隆项目
git clone <repository-url>
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问应用
# http://localhost:5173
```

## 📁 项目结构详解

```
src/
├── components/          # 可复用组件
│   ├── Layout/         # 布局相关组件
│   │   ├── MainLayout.tsx      # 主布局
│   │   └── MainLayout.css      # 布局样式
│   └── ProtectedRoute.tsx      # 路由保护组件
├── contexts/           # React Context
│   └── AuthContext.tsx         # 认证上下文
├── hooks/              # 自定义Hooks
│   └── useTable.ts             # 表格状态管理Hook
├── pages/              # 页面组件
│   ├── Login.tsx               # 登录页
│   ├── Dashboard.tsx           # 仪表板
│   ├── StudentManagement.tsx   # 学生管理
│   ├── TeacherManagement.tsx   # 教师管理
│   ├── CourseManagement.tsx    # 课程管理
│   ├── Profile.tsx             # 个人信息
│   └── NotFound.tsx            # 404页面
├── services/           # API服务层
│   ├── api.ts                  # HTTP客户端配置
│   ├── authService.ts          # 认证服务
│   ├── studentService.ts       # 学生服务
│   ├── teacherService.ts       # 教师服务
│   ├── courseService.ts        # 课程服务
│   └── mockService.ts          # Mock数据服务
├── types/              # TypeScript类型定义
│   └── index.ts                # 通用类型
├── utils/              # 工具函数
│   └── index.ts                # 通用工具
├── constants/          # 常量定义
│   └── index.ts                # 应用常量
├── App.tsx             # 根组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 🛠 开发规范

### 代码风格
- 使用TypeScript进行类型安全开发
- 遵循ESLint规则
- 使用Prettier格式化代码
- 组件名使用PascalCase
- 文件名使用PascalCase（组件）或camelCase（工具）

### 组件开发规范
```tsx
// 好的组件示例
import React, { useState, useEffect } from 'react';
import { Button, Card } from 'antd';
import { User } from '../types';

interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  className?: string;
}

const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  className 
}) => {
  const [loading, setLoading] = useState(false);

  const handleEdit = () => {
    if (onEdit) {
      onEdit(user);
    }
  };

  return (
    <Card className={className}>
      <h3>{user.name}</h3>
      <Button onClick={handleEdit} loading={loading}>
        编辑
      </Button>
    </Card>
  );
};

export default UserCard;
```

### API服务开发规范
```typescript
// API服务示例
import { api } from './api';
import { User, ApiResponse } from '../types';

export const userService = {
  // 获取用户列表
  getUsers: async (): Promise<User[]> => {
    const response = await api.get<ApiResponse<User[]>>('/users');
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取用户列表失败');
    }
  },

  // 创建用户
  createUser: async (user: Omit<User, 'id'>): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/users', user);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '创建用户失败');
    }
  }
};
```

### 状态管理规范
- 使用Context API管理全局状态
- 组件内部状态使用useState
- 复杂状态逻辑使用useReducer
- 避免过度使用全局状态

### 错误处理规范
```typescript
// 统一错误处理
try {
  const result = await apiCall();
  message.success('操作成功');
} catch (error: any) {
  const errorMessage = error.message || '操作失败';
  message.error(errorMessage);
  console.error('API Error:', error);
}
```

## 🎨 UI/UX 开发指南

### 设计原则
- 保持界面简洁清晰
- 遵循Ant Design设计语言
- 确保响应式设计
- 注重用户体验

### 颜色规范
```css
/* 主色调 */
--primary-color: #1890ff;
--success-color: #52c41a;
--warning-color: #faad14;
--error-color: #f5222d;

/* 中性色 */
--text-color: rgba(0, 0, 0, 0.85);
--text-color-secondary: rgba(0, 0, 0, 0.45);
--border-color: #d9d9d9;
--background-color: #f0f2f5;
```

### 间距规范
- 基础间距单位：8px
- 常用间距：8px, 16px, 24px, 32px
- 页面边距：24px
- 组件间距：16px

### 响应式断点
```css
/* 移动端 */
@media (max-width: 767px) { }

/* 平板端 */
@media (min-width: 768px) and (max-width: 1199px) { }

/* 桌面端 */
@media (min-width: 1200px) { }
```

## 🔧 开发工具配置

### VS Code 推荐插件
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
- Prettier - Code formatter
- ESLint

### 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}/src"
    }
  ]
}
```

## 🧪 测试指南

### 单元测试
```typescript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import UserCard from '../UserCard';

const mockUser = {
  id: 1,
  name: '测试用户',
  email: '<EMAIL>'
};

test('renders user card', () => {
  render(<UserCard user={mockUser} />);
  expect(screen.getByText('测试用户')).toBeInTheDocument();
});

test('calls onEdit when edit button clicked', () => {
  const mockOnEdit = jest.fn();
  render(<UserCard user={mockUser} onEdit={mockOnEdit} />);
  
  fireEvent.click(screen.getByText('编辑'));
  expect(mockOnEdit).toHaveBeenCalledWith(mockUser);
});
```

### API测试
```typescript
// API服务测试
import { userService } from '../userService';

// Mock axios
jest.mock('../api');

test('should fetch users successfully', async () => {
  const mockUsers = [{ id: 1, name: '用户1' }];
  
  // Mock API response
  (api.get as jest.Mock).mockResolvedValue({
    success: true,
    data: mockUsers
  });

  const users = await userService.getUsers();
  expect(users).toEqual(mockUsers);
});
```

## 📦 构建与部署

### 开发环境
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览生产版本
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

### 环境变量配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_ENV=development

# .env.production
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_ENV=production
```

### Docker部署
```bash
# 构建镜像
docker build -t university-frontend .

# 运行容器
docker run -p 80:80 university-frontend
```

## 🐛 常见问题

### 1. 开发服务器启动失败
```bash
# 清除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 2. TypeScript类型错误
- 确保所有类型定义正确
- 检查import路径是否正确
- 运行`npm run type-check`检查类型

### 3. API请求失败
- 检查后端服务是否启动
- 确认API地址配置正确
- 检查网络连接和跨域设置

### 4. 样式问题
- 确保CSS类名正确
- 检查Ant Design主题配置
- 使用浏览器开发者工具调试

## 📚 学习资源

- [React官方文档](https://react.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Ant Design官方文档](https://ant.design/)
- [Vite官方文档](https://vitejs.dev/)
- [React Router官方文档](https://reactrouter.com/)

## 🤝 贡献流程

1. Fork项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 📞 技术支持

如有问题，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至开发团队
- 在团队聊天群中讨论

---

**Happy Coding! 🎉**
