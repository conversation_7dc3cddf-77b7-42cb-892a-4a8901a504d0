package com.university.logistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.logistics.entity.Maintenance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维修申请数据访问层接口
 */
public interface MaintenanceMapper extends BaseMapper<Maintenance> {

    /**
     * 根据申请人ID查询维修申请列表
     */
    List<Maintenance> selectByApplicantId(@Param("applicantId") Long applicantId);

    /**
     * 根据状态查询维修申请列表
     */
    List<Maintenance> selectByStatus(@Param("status") Integer status);

    /**
     * 根据维修类型查询维修申请列表
     */
    List<Maintenance> selectByMaintenanceType(@Param("maintenanceType") Integer maintenanceType);

    /**
     * 分页查询维修申请列表
     */
    IPage<Maintenance> selectMaintenancePage(IPage<Maintenance> page, @Param("maintenance") Maintenance maintenance);
}