package com.university.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.course.entity.Course;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程数据访问层接口
 */
public interface CourseMapper extends BaseMapper<Course> {

    /**
     * 根据课程编号查询课程信息
     */
    Course selectByCourseNo(@Param("courseNo") String courseNo);

    /**
     * 根据学院ID查询课程列表
     */
    List<Course> selectByCollegeId(@Param("collegeId") Long collegeId);

    /**
     * 根据专业ID查询课程列表
     */
    List<Course> selectByMajorId(@Param("majorId") Long majorId);

    /**
     * 根据教师ID查询课程列表
     */
    List<Course> selectByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 分页查询课程列表
     */
    IPage<Course> selectCoursePage(IPage<Course> page, @Param("course") Course course);
}