package com.university.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.finance.entity.Finance;
import com.university.finance.service.FinanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务管理控制器
 */
@RestController
@RequestMapping("/finance")
public class FinanceController {

    @Autowired
    private FinanceService financeService;

    /**
     * 根据ID查询财务记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getFinanceById(@PathVariable Long id) {
        Finance finance = financeService.getById(id);
        if (finance == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("财务记录不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(finance));
    }

    /**
     * 根据学生ID查询财务记录列表
     */
    @GetMapping("/student/{studentId}")
    public ResponseEntity<?> getFinanceByStudent(@PathVariable Long studentId) {
        List<Finance> finances = financeService.getByStudentId(studentId);
        return ResponseEntity.ok(createSuccessResponse(finances));
    }

    /**
     * 根据交易类型查询财务记录列表
     */
    @GetMapping("/type/{transactionType}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getFinanceByType(@PathVariable Integer transactionType) {
        List<Finance> finances = financeService.getByTransactionType(transactionType);
        return ResponseEntity.ok(createSuccessResponse(finances));
    }

    /**
     * 分页查询财务记录列表
     */
    @GetMapping("/page")
    @PreAuthorize("hasAnyRole('ADMIN', 'FINANCE')")
    public ResponseEntity<?> getFinancePage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            Finance finance) {

        Page<Finance> page = new Page<>(pageNum, pageSize);
        IPage<Finance> financePage = financeService.getFinancePage(page, finance);
        return ResponseEntity.ok(createSuccessResponse(financePage));
    }

    /**
     * 创建财务记录
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'FINANCE')")
    public ResponseEntity<?> createFinance(@RequestBody Finance finance) {
        if (finance.getStudentId() == null || finance.getAmount() == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("学生ID和金额不能为空"));
        }

        boolean saved = financeService.save(finance);
        if (saved) {
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(createSuccessResponse(finance));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建财务记录失败"));
        }
    }

    /**
     * 更新财务记录
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'FINANCE')")
    public ResponseEntity<?> updateFinance(@PathVariable Long id, @RequestBody Finance finance) {
        Finance existingFinance = financeService.getById(id);
        if (existingFinance == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("财务记录不存在"));
        }

        finance.setId(id);
        boolean updated = financeService.updateById(finance);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse(finance));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新财务记录失败"));
        }
    }

    /**
     * 更新财务记录状态
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateFinanceStatus(@PathVariable Long id, @RequestBody Map<String, Object> statusData) {
        Integer status = (Integer) statusData.get("status");
        if (status == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("状态参数不能为空"));
        }

        boolean updated = financeService.updateFinanceStatus(id, status);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse("状态更新成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("状态更新失败"));
        }
    }

    /**
     * 生成学生财务报表
     */
    @GetMapping("/report/student/{studentId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'FINANCE', 'STUDENT')")
    public ResponseEntity<?> generateStudentReport(@PathVariable Long studentId) {
        Map<String, Object> report = financeService.generateStudentFinancialReport(studentId);
        return ResponseEntity.ok(createSuccessResponse(report));
    }

    /**
     * 统计指定类型交易总额
     */
    @GetMapping("/total/type/{transactionType}")
    @PreAuthorize("hasAnyRole('ADMIN', 'FINANCE')")
    public ResponseEntity<?> getTotalByType(@PathVariable Integer transactionType) {
        BigDecimal total = financeService.getTotalAmountByType(transactionType);
        return ResponseEntity.ok(createSuccessResponse(total));
    }

    /**
     * 删除财务记录
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteFinance(@PathVariable Long id) {
        Finance finance = financeService.getById(id);
        if (finance == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("财务记录不存在"));
        }

        boolean deleted = financeService.removeById(id);
        if (deleted) {
            return ResponseEntity.ok(createSuccessResponse("删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("删除失败"));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("code", 200);
        response.put("message", "操作成功");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}