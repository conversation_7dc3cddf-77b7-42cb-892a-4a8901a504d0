package com.university.student.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.student.entity.Student;
import com.university.student.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生管理控制器
 */
@RestController
@RequestMapping("/api/students")
public class StudentController {

    @Autowired
    private StudentService studentService;

    /**
     * 根据ID获取学生信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getStudentById(@PathVariable Long id) {
        Student student = studentService.getStudentDetail(id);
        if (student == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("学生不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(student));
    }

    /**
     * 根据学号获取学生信息
     */
    @GetMapping("/student-no/{studentNo}")
    public ResponseEntity<?> getStudentByNo(@PathVariable String studentNo) {
        Student student = studentService.getByStudentNo(studentNo);
        if (student == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("学生不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(student));
    }

    /**
     * 分页查询学生列表
     */
    @GetMapping("/page")
    public ResponseEntity<?> getStudentPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            Student student) {

        Page<Student> page = new Page<>(pageNum, pageSize);
        Page<Student> studentPage = studentService.getStudentPage(page, student);
        return ResponseEntity.ok(createSuccessResponse(studentPage));
    }

    /**
     * 根据学院ID查询学生
     */
    @GetMapping("/college/{collegeId}")
    public ResponseEntity<?> getStudentsByCollege(@PathVariable Long collegeId) {
        List<Student> students = studentService.getByCollegeId(collegeId);
        return ResponseEntity.ok(createSuccessResponse(students));
    }

    /**
     * 根据专业ID查询学生
     */
    @GetMapping("/major/{majorId}")
    public ResponseEntity<?> getStudentsByMajor(@PathVariable Long majorId) {
        List<Student> students = studentService.getByMajorId(majorId);
        return ResponseEntity.ok(createSuccessResponse(students));
    }

    /**
     * 根据班级ID查询学生
     */
    @GetMapping("/class/{classId}")
    public ResponseEntity<?> getStudentsByClass(@PathVariable Long classId) {
        List<Student> students = studentService.getByClassId(classId);
        return ResponseEntity.ok(createSuccessResponse(students));
    }

    /**
     * 创建学生信息
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> createStudent(@RequestBody Student student) {
        if (StrUtil.isEmpty(student.getStudentNo())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("学号不能为空"));
        }

        // 检查学号是否已存在
        if (studentService.getByStudentNo(student.getStudentNo()) != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(createErrorResponse("学号已存在"));
        }

        boolean saved = studentService.save(student);
        if (saved) {
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(createSuccessResponse(student));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建学生失败"));
        }
    }

    /**
     * 更新学生信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> updateStudent(
            @PathVariable Long id,
            @RequestBody Student student) {
        student.setId(id);
        boolean updated = studentService.updateById(student);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse(student));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新学生失败"));
        }
    }

    /**
     * 更新学生状态
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> updateStudentStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        boolean updated = studentService.updateStudentStatus(id, status);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse("学生状态更新成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新学生状态失败"));
        }
    }

    /**
     * 删除学生信息
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteStudent(@PathVariable Long id) {
        boolean deleted = studentService.removeById(id);
        if (deleted) {
            return ResponseEntity.ok(createSuccessResponse("学生删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("删除学生失败"));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "操作成功");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}