package com.university.course.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.course.entity.Course;
import com.university.course.mapper.CourseMapper;
import com.university.course.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 课程管理业务逻辑实现类
 */
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {

    @Autowired
    private CourseMapper courseMapper;

    @Override
    public Course getByCourseNo(String courseNo) {
        return courseMapper.selectByCourseNo(courseNo);
    }

    @Override
    public List<Course> getByCollegeId(Long collegeId) {
        return courseMapper.selectByCollegeId(collegeId);
    }

    @Override
    public List<Course> getByMajorId(Long majorId) {
        return courseMapper.selectByMajorId(majorId);
    }

    @Override
    public List<Course> getByTeacherId(Long teacherId) {
        return courseMapper.selectByTeacherId(teacherId);
    }

    @Override
    public Page<Course> getCoursePage(Page<Course> page, Course course) {
        IPage<Course> result = courseMapper.selectCoursePage(page, course);
        return (Page<Course>) result;
    }

    @Override
    public boolean updateCourseStatus(Long id, Integer status) {
        Course course = new Course();
        course.setId(id);
        course.setStatus(status);
        return updateById(course);
    }

    @Override
    public Course getCourseDetail(Long id) {
        return getById(id);
    }
}