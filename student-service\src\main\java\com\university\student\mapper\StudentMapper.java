package com.university.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.student.entity.Student;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 学生数据访问接口
 */
@Repository
public interface StudentMapper extends BaseMapper<Student> {
    /**
     * 根据学号查询学生
     */
    Student selectByStudentNo(@Param("studentNo") String studentNo);

    /**
     * 根据学院ID查询学生
     */
    List<Student> selectByCollegeId(@Param("collegeId") Long collegeId);

    /**
     * 根据专业ID查询学生
     */
    List<Student> selectByMajorId(@Param("majorId") Long majorId);

    /**
     * 根据班级ID查询学生
     */
    List<Student> selectByClassId(@Param("classId") Long classId);

    /**
     * 分页查询学生列表
     */
    IPage<Student> selectStudentPage(Page<Student> page, @Param("student") Student student);
}