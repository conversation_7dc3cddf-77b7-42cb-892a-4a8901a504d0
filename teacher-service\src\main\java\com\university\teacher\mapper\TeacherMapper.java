package com.university.teacher.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.teacher.entity.Teacher;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师数据访问层接口
 */
public interface TeacherMapper extends BaseMapper<Teacher> {

    /**
     * 根据教师工号查询教师信息
     */
    Teacher selectByTeacherNo(@Param("teacherNo") String teacherNo);

    /**
     * 根据学院ID查询教师列表
     */
    List<Teacher> selectByCollegeId(@Param("collegeId") Long collegeId);

    /**
     * 根据专业ID查询教师列表
     */
    List<Teacher> selectByMajorId(@Param("majorId") Long majorId);

    /**
     * 分页查询教师列表
     */
    IPage<Teacher> selectTeacherPage(IPage<Teacher> page, @Param("teacher") Teacher teacher);
}