package com.university.teacher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.university.teacher.entity.Teacher;
import com.university.teacher.mapper.TeacherMapper;
import com.university.teacher.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教师管理业务逻辑实现类
 */
@Service
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements TeacherService {

    @Autowired
    private TeacherMapper teacherMapper;

    @Override
    public Teacher getByTeacherNo(String teacherNo) {
        return teacherMapper.selectByTeacherNo(teacherNo);
    }

    @Override
    public List<Teacher> getByCollegeId(Long collegeId) {
        return teacherMapper.selectByCollegeId(collegeId);
    }

    @Override
    public List<Teacher> getByMajorId(Long majorId) {
        return teacherMapper.selectByMajorId(majorId);
    }

    @Override
    public IPage<Teacher> getTeacherPage(Page<Teacher> page, Teacher teacher) {
        return teacherMapper.selectTeacherPage(page, teacher);
    }

    @Override
    public boolean updateTeacherStatus(Long id, Integer status) {
        Teacher teacher = new Teacher();
        teacher.setId(id);
        teacher.setStatus(status);
        return updateById(teacher);
    }

    @Override
    public Teacher getTeacherDetail(Long id) {
        return getById(id);
    }
}