package com.university.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.university.auth.entity.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserMapper extends BaseMapper<User> {
    /**
     * 根据用户名查询用户信息
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询角色信息
     */
    User selectUserWithRoles(@Param("userId") Long userId);
}