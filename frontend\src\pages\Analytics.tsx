import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Space,
  Progress,
  Table,
  Tag,
  Button,
  Divider
} from 'antd';
import {
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  TrophyOutlined,
  TeamOutlined,
  BookOutlined,
  UserOutlined,
  RiseOutlined,
  FallOutlined,
  DownloadOutlined,
  PrinterOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface AnalyticsData {
  totalStudents: number;
  totalTeachers: number;
  totalCourses: number;
  averageGrade: number;
  passRate: number;
  excellentRate: number;
  enrollmentTrend: number;
  graduationRate: number;
}

interface DepartmentStats {
  id: number;
  department: string;
  students: number;
  teachers: number;
  courses: number;
  averageGrade: number;
  passRate: number;
}

interface CourseStats {
  id: number;
  courseCode: string;
  courseName: string;
  enrollments: number;
  averageScore: number;
  passRate: number;
  difficulty: string;
}

const Analytics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('semester');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');

  // 模拟数据
  const analyticsData: AnalyticsData = {
    totalStudents: 1250,
    totalTeachers: 85,
    totalCourses: 156,
    averageGrade: 85.8,
    passRate: 94.2,
    excellentRate: 28.5,
    enrollmentTrend: 12.5,
    graduationRate: 96.8
  };

  const departmentStats: DepartmentStats[] = [
    {
      id: 1,
      department: '计算机科学与技术',
      students: 320,
      teachers: 18,
      courses: 45,
      averageGrade: 87.2,
      passRate: 96.5
    },
    {
      id: 2,
      department: '数学与应用数学',
      students: 280,
      teachers: 15,
      courses: 38,
      averageGrade: 84.6,
      passRate: 93.8
    },
    {
      id: 3,
      department: '物理学',
      students: 220,
      teachers: 12,
      courses: 32,
      averageGrade: 83.1,
      passRate: 91.2
    },
    {
      id: 4,
      department: '化学',
      students: 200,
      teachers: 11,
      courses: 28,
      averageGrade: 85.9,
      passRate: 94.8
    },
    {
      id: 5,
      department: '生物科学',
      students: 230,
      teachers: 13,
      courses: 35,
      averageGrade: 86.4,
      passRate: 95.2
    }
  ];

  const courseStats: CourseStats[] = [
    {
      id: 1,
      courseCode: 'CS101',
      courseName: '计算机科学导论',
      enrollments: 120,
      averageScore: 87.5,
      passRate: 96.7,
      difficulty: '中等'
    },
    {
      id: 2,
      courseCode: 'MATH101',
      courseName: '高等数学A',
      enrollments: 200,
      averageScore: 82.3,
      passRate: 89.5,
      difficulty: '困难'
    },
    {
      id: 3,
      courseCode: 'PHYS101',
      courseName: '大学物理',
      enrollments: 180,
      averageScore: 84.1,
      passRate: 92.2,
      difficulty: '中等'
    },
    {
      id: 4,
      courseCode: 'ENG101',
      courseName: '大学英语',
      enrollments: 300,
      averageScore: 88.9,
      passRate: 97.3,
      difficulty: '简单'
    },
    {
      id: 5,
      courseCode: 'CHEM101',
      courseName: '无机化学',
      enrollments: 150,
      averageScore: 85.7,
      passRate: 94.0,
      difficulty: '中等'
    }
  ];

  const departmentColumns: ColumnsType<DepartmentStats> = [
    {
      title: '学院/专业',
      dataIndex: 'department',
      key: 'department',
      width: 200,
    },
    {
      title: '学生数',
      dataIndex: 'students',
      key: 'students',
      width: 100,
      render: (value: number) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{value}</span>
      ),
    },
    {
      title: '教师数',
      dataIndex: 'teachers',
      key: 'teachers',
      width: 100,
      render: (value: number) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>{value}</span>
      ),
    },
    {
      title: '课程数',
      dataIndex: 'courses',
      key: 'courses',
      width: 100,
      render: (value: number) => (
        <span style={{ fontWeight: 'bold', color: '#faad14' }}>{value}</span>
      ),
    },
    {
      title: '平均成绩',
      dataIndex: 'averageGrade',
      key: 'averageGrade',
      width: 120,
      render: (value: number) => (
        <span style={{ 
          fontWeight: 'bold',
          color: value >= 85 ? '#52c41a' : value >= 80 ? '#1890ff' : '#faad14'
        }}>
          {value.toFixed(1)}
        </span>
      ),
    },
    {
      title: '及格率',
      dataIndex: 'passRate',
      key: 'passRate',
      width: 120,
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          status={value >= 95 ? 'success' : value >= 90 ? 'normal' : 'exception'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
  ];

  const courseColumns: ColumnsType<CourseStats> = [
    {
      title: '课程编号',
      dataIndex: 'courseCode',
      key: 'courseCode',
      width: 120,
    },
    {
      title: '课程名称',
      dataIndex: 'courseName',
      key: 'courseName',
      width: 180,
    },
    {
      title: '选课人数',
      dataIndex: 'enrollments',
      key: 'enrollments',
      width: 100,
      render: (value: number) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{value}</span>
      ),
    },
    {
      title: '平均分',
      dataIndex: 'averageScore',
      key: 'averageScore',
      width: 100,
      render: (value: number) => (
        <span style={{ 
          fontWeight: 'bold',
          color: value >= 85 ? '#52c41a' : value >= 80 ? '#1890ff' : '#faad14'
        }}>
          {value.toFixed(1)}
        </span>
      ),
    },
    {
      title: '及格率',
      dataIndex: 'passRate',
      key: 'passRate',
      width: 120,
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          status={value >= 95 ? 'success' : value >= 90 ? 'normal' : 'exception'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '难度',
      dataIndex: 'difficulty',
      key: 'difficulty',
      width: 100,
      render: (difficulty: string) => {
        const color = difficulty === '简单' ? 'green' : difficulty === '中等' ? 'blue' : 'red';
        return <Tag color={color}>{difficulty}</Tag>;
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="数据分析" style={{ marginBottom: '24px' }}>
        {/* 筛选条件 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Space>
              <span>统计周期：</span>
              <Select
                value={selectedPeriod}
                onChange={setSelectedPeriod}
                style={{ width: 120 }}
              >
                <Option value="semester">本学期</Option>
                <Option value="year">本学年</Option>
                <Option value="all">全部</Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <span>学院筛选：</span>
              <Select
                value={selectedDepartment}
                onChange={setSelectedDepartment}
                style={{ width: 150 }}
              >
                <Option value="all">全部学院</Option>
                <Option value="cs">计算机科学与技术</Option>
                <Option value="math">数学与应用数学</Option>
                <Option value="physics">物理学</Option>
                <Option value="chemistry">化学</Option>
                <Option value="biology">生物科学</Option>
              </Select>
            </Space>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              <Button icon={<DownloadOutlined />}>导出报告</Button>
              <Button icon={<PrinterOutlined />}>打印报告</Button>
            </Space>
          </Col>
        </Row>

        {/* 核心指标统计 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="学生总数"
                value={analyticsData.totalStudents}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#1890ff' }}
                suffix={
                  <span style={{ fontSize: '14px', color: '#52c41a' }}>
                    <RiseOutlined /> +{analyticsData.enrollmentTrend}%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="教师总数"
                value={analyticsData.totalTeachers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="课程总数"
                value={analyticsData.totalCourses}
                prefix={<BookOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="毕业率"
                value={analyticsData.graduationRate}
                precision={1}
                suffix="%"
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 学术表现指标 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={8}>
            <Card title="平均成绩" extra={<BarChartOutlined />}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#1890ff' }}>
                  {analyticsData.averageGrade}
                </div>
                <div style={{ color: '#666', marginTop: '8px' }}>
                  较上学期提升 2.3 分
                </div>
                <Progress
                  percent={analyticsData.averageGrade}
                  showInfo={false}
                  strokeColor="#1890ff"
                  style={{ marginTop: '16px' }}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="及格率" extra={<LineChartOutlined />}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#52c41a' }}>
                  {analyticsData.passRate}%
                </div>
                <div style={{ color: '#666', marginTop: '8px' }}>
                  较上学期提升 1.8%
                </div>
                <Progress
                  percent={analyticsData.passRate}
                  showInfo={false}
                  strokeColor="#52c41a"
                  style={{ marginTop: '16px' }}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="优秀率" extra={<PieChartOutlined />}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#faad14' }}>
                  {analyticsData.excellentRate}%
                </div>
                <div style={{ color: '#666', marginTop: '8px' }}>
                  较上学期提升 3.2%
                </div>
                <Progress
                  percent={analyticsData.excellentRate}
                  showInfo={false}
                  strokeColor="#faad14"
                  style={{ marginTop: '16px' }}
                />
              </div>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 学院统计表格 */}
        <Card title="学院统计分析" style={{ marginBottom: '24px' }}>
          <Table
            columns={departmentColumns}
            dataSource={departmentStats}
            rowKey="id"
            pagination={false}
            size="middle"
          />
        </Card>

        {/* 课程统计表格 */}
        <Card title="热门课程分析">
          <Table
            columns={courseColumns}
            dataSource={courseStats}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            size="middle"
          />
        </Card>
      </Card>
    </div>
  );
};

export default Analytics;
