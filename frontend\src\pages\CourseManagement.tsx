import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  BookOutlined,
  UserOutlined,
  TeamOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { Course, PageResponse } from '../types';
import { courseService } from '../services/courseService';

const { Option } = Select;
const { Title } = Typography;

const CourseManagement: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<number | undefined>();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [form] = Form.useForm();

  // 统计数据
  const [statistics, setStatistics] = useState({
    total: 0,
    required: 0,
    elective: 0,
    totalCredits: 0
  });

  useEffect(() => {
    fetchCourses();
  }, [pagination.current, pagination.pageSize, searchText, selectedType]);

  const fetchCourses = async () => {
    setLoading(true);
    try {
      const params = {
        current: pagination.current,
        size: pagination.pageSize,
        courseName: searchText || undefined,
        courseType: selectedType
      };

      const response: PageResponse<Course> = await courseService.getCoursePage(params);
      
      setCourses(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      // 更新统计数据
      const totalCredits = response.records.reduce((sum, course) => sum + course.credits, 0);
      setStatistics({
        total: response.total,
        required: response.records.filter(c => c.courseType === 1).length,
        elective: response.records.filter(c => c.courseType === 2).length,
        totalCredits
      });

    } catch (error) {
      message.error('获取课程列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingCourse(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: Course) => {
    setEditingCourse(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await courseService.deleteCourse(id);
      message.success('删除成功');
      fetchCourses();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingCourse) {
        await courseService.updateCourse(editingCourse.id, values);
        message.success('更新成功');
      } else {
        await courseService.createCourse(values);
        message.success('创建成功');
      }

      setModalVisible(false);
      fetchCourses();
    } catch (error) {
      message.error(editingCourse ? '更新失败' : '创建失败');
    }
  };

  const handleStatusChange = async (id: number, status: number) => {
    try {
      await courseService.updateCourseStatus(id, status);
      message.success('状态更新成功');
      fetchCourses();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const columns = [
    {
      title: '课程编号',
      dataIndex: 'courseNo',
      key: 'courseNo',
      width: 120,
      fixed: 'left' as const
    },
    {
      title: '课程名称',
      dataIndex: 'courseName',
      key: 'courseName',
      width: 200
    },
    {
      title: '学分',
      dataIndex: 'credits',
      key: 'credits',
      width: 80,
      align: 'center' as const
    },
    {
      title: '学时',
      dataIndex: 'hours',
      key: 'hours',
      width: 80,
      align: 'center' as const
    },
    {
      title: '课程类型',
      dataIndex: 'courseType',
      key: 'courseType',
      width: 100,
      render: (type: number) => (
        <Tag color={type === 1 ? 'red' : 'blue'}>
          {type === 1 ? '必修' : '选修'}
        </Tag>
      )
    },
    {
      title: '学院',
      dataIndex: ['department', 'deptName'],
      key: 'department',
      width: 150
    },
    {
      title: '任课教师',
      dataIndex: ['teacher', 'name'],
      key: 'teacher',
      width: 120
    },
    {
      title: '学期',
      dataIndex: 'semester',
      key: 'semester',
      width: 100
    },
    {
      title: '容量',
      key: 'capacity',
      width: 120,
      render: (_, record: Course) => (
        <span>
          {record.currentStudents || 0}/{record.maxStudents || 0}
        </span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: Course) => (
        <Select
          value={status}
          size="small"
          style={{ width: 80 }}
          onChange={(value) => handleStatusChange(record.id, value)}
        >
          <Option value={1}>
            <Tag color="green">开课</Tag>
          </Option>
          <Option value={0}>
            <Tag color="red">停课</Tag>
          </Option>
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Course) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个课程吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="课程总数"
              value={statistics.total}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="必修课程"
              value={statistics.required}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="选修课程"
              value={statistics.elective}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总学分"
              value={statistics.totalCredits}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space>
                <Input
                  placeholder="搜索课程名称或编号"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                  allowClear
                />
                <Select
                  placeholder="选择类型"
                  value={selectedType}
                  onChange={setSelectedType}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Option value={1}>必修</Option>
                  <Option value={2}>选修</Option>
                </Select>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  新增课程
                </Button>
                <Button icon={<ExportOutlined />}>
                  导出数据
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={courses}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10
              }));
            }
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingCourse ? '编辑课程' : '新增课程'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="courseNo"
                label="课程编号"
                rules={[{ required: true, message: '请输入课程编号' }]}
              >
                <Input placeholder="请输入课程编号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="courseName"
                label="课程名称"
                rules={[{ required: true, message: '请输入课程名称' }]}
              >
                <Input placeholder="请输入课程名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="credits"
                label="学分"
                rules={[{ required: true, message: '请输入学分' }]}
              >
                <InputNumber
                  min={1}
                  max={10}
                  style={{ width: '100%' }}
                  placeholder="学分"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="hours"
                label="学时"
                rules={[{ required: true, message: '请输入学时' }]}
              >
                <InputNumber
                  min={1}
                  max={200}
                  style={{ width: '100%' }}
                  placeholder="学时"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="courseType"
                label="课程类型"
                rules={[{ required: true, message: '请选择课程类型' }]}
              >
                <Select placeholder="请选择课程类型">
                  <Option value={1}>必修</Option>
                  <Option value={2}>选修</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="departmentId"
                label="开课学院"
                rules={[{ required: true, message: '请选择开课学院' }]}
              >
                <Select placeholder="请选择开课学院">
                  <Option value={1}>计算机学院</Option>
                  <Option value={2}>数学学院</Option>
                  <Option value={3}>物理学院</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="teacherId"
                label="任课教师"
              >
                <Select placeholder="请选择任课教师" allowClear>
                  <Option value={1}>张教授</Option>
                  <Option value={2}>李教授</Option>
                  <Option value={3}>王教授</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="semester"
                label="开课学期"
                rules={[{ required: true, message: '请输入开课学期' }]}
              >
                <Input placeholder="如：2024春季" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxStudents"
                label="最大容量"
              >
                <InputNumber
                  min={1}
                  max={500}
                  style={{ width: '100%' }}
                  placeholder="最大学生数"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="课程描述"
          >
            <Input.TextArea rows={3} placeholder="请输入课程描述" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value={1}>开课</Option>
              <Option value={0}>停课</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCourse ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CourseManagement;
