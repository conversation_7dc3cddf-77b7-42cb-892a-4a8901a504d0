package com.university.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.university.finance.entity.Finance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 财务记录数据访问层接口
 */
public interface FinanceMapper extends BaseMapper<Finance> {

    /**
     * 根据学生ID查询财务记录列表
     */
    List<Finance> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据交易类型查询财务记录列表
     */
    List<Finance> selectByTransactionType(@Param("transactionType") Integer transactionType);

    /**
     * 根据状态查询财务记录列表
     */
    List<Finance> selectByStatus(@Param("status") Integer status);

    /**
     * 分页查询财务记录列表
     */
    IPage<Finance> selectFinancePage(IPage<Finance> page, @Param("finance") Finance finance);
}