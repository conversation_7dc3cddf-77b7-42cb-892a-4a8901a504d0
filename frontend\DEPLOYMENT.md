# 大学管理系统前端部署指南

## 🚀 部署概述

本文档详细说明了大学管理系统前端的各种部署方式，包括开发环境、生产环境和容器化部署。

## 📋 部署前准备

### 系统要求
- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+
- Git 2.0+
- Docker 20.0+ (容器化部署)

### 环境检查
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查Git版本
git --version

# 检查Docker版本（可选）
docker --version
```

## 🛠 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd frontend
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
# 复制环境配置文件
cp .env.development .env.local

# 编辑配置文件
vim .env.local
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

## 🏭 生产环境部署

### 方式一：传统部署

#### 1. 构建项目
```bash
# 安装依赖
npm ci --only=production

# 构建生产版本
npm run build
```

#### 2. 配置Web服务器

**Nginx配置示例：**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**Apache配置示例：**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/dist
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
    
    # SPA路由支持
    <Directory "/path/to/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

### 方式二：Docker部署

#### 1. 构建Docker镜像
```bash
# 构建镜像
docker build -t university-frontend:latest .

# 查看镜像
docker images
```

#### 2. 运行容器
```bash
# 运行容器
docker run -d \
  --name university-frontend \
  -p 80:80 \
  university-frontend:latest

# 查看运行状态
docker ps
```

#### 3. 使用Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式三：CDN部署

#### 1. 构建并上传
```bash
# 构建项目
npm run build

# 上传dist目录到CDN
# 例如：阿里云OSS、腾讯云COS、AWS S3等
```

#### 2. 配置CDN
- 设置默认首页为 index.html
- 配置404错误页面重定向到 index.html
- 启用Gzip压缩
- 设置静态资源缓存策略

## 🔧 环境配置

### 开发环境 (.env.development)
```bash
VITE_APP_TITLE=大学管理系统
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_ENV=development
VITE_USE_MOCK=false
VITE_DEBUG=true
```

### 生产环境 (.env.production)
```bash
VITE_APP_TITLE=大学管理系统
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_APP_ENV=production
VITE_USE_MOCK=false
VITE_DEBUG=false
```

### 测试环境 (.env.test)
```bash
VITE_APP_TITLE=大学管理系统-测试
VITE_API_BASE_URL=https://test-api.yourdomain.com/api
VITE_APP_ENV=test
VITE_USE_MOCK=true
VITE_DEBUG=true
```

## 📊 性能优化

### 构建优化
```javascript
// vite.config.ts
export default defineConfig({
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          router: ['react-router-dom']
        }
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

### 缓存策略
- HTML文件：no-cache
- JS/CSS文件：1年强缓存
- 图片资源：1年强缓存
- API接口：根据业务需求设置

## 🔍 监控与日志

### 前端监控
```javascript
// 错误监控
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // 发送错误信息到监控服务
});

// 性能监控
window.addEventListener('load', () => {
  const perfData = performance.getEntriesByType('navigation')[0];
  console.log('Page load time:', perfData.loadEventEnd - perfData.fetchStart);
});
```

### Nginx访问日志
```nginx
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

access_log /var/log/nginx/access.log main;
error_log /var/log/nginx/error.log;
```

## 🛡 安全配置

### HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

### 内容安全策略
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:;">
```

## 🚨 故障排除

### 常见问题

#### 1. 白屏问题
```bash
# 检查控制台错误
# 检查网络请求
# 检查路由配置
# 检查API地址配置
```

#### 2. 路由404问题
```nginx
# 确保Nginx配置了SPA路由支持
location / {
    try_files $uri $uri/ /index.html;
}
```

#### 3. API跨域问题
```javascript
// 开发环境代理配置
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
});
```

#### 4. 静态资源加载失败
```javascript
// 检查publicPath配置
export default defineConfig({
  base: '/your-app-path/'
});
```

## 📈 部署检查清单

### 部署前检查
- [ ] 代码已提交到版本控制
- [ ] 环境变量配置正确
- [ ] API地址配置正确
- [ ] 构建成功无错误
- [ ] 本地预览正常

### 部署后检查
- [ ] 页面可以正常访问
- [ ] 登录功能正常
- [ ] API请求正常
- [ ] 路由跳转正常
- [ ] 静态资源加载正常
- [ ] 响应式布局正常
- [ ] 浏览器兼容性正常

## 📞 技术支持

如遇到部署问题，请：
1. 查看浏览器控制台错误
2. 检查服务器日志
3. 确认网络连接
4. 联系技术支持团队

---

**部署成功后，记得更新相关文档和通知相关人员！** 🎉
