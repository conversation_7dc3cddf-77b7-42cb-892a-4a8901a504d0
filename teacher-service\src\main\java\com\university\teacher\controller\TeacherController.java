package com.university.teacher.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.university.teacher.entity.Teacher;
import com.university.teacher.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教师管理控制器
 */
@RestController
@RequestMapping("/api/teachers")
public class TeacherController {

    @Autowired
    private TeacherService teacherService;

    /**
     * 根据ID获取教师信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTeacherById(@PathVariable Long id) {
        Teacher teacher = teacherService.getTeacherDetail(id);
        if (teacher == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("教师不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(teacher));
    }

    /**
     * 根据教师工号获取教师信息
     */
    @GetMapping("/teacher-no/{teacherNo}")
    public ResponseEntity<?> getTeacherByNo(@PathVariable String teacherNo) {
        Teacher teacher = teacherService.getByTeacherNo(teacherNo);
        if (teacher == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(createErrorResponse("教师不存在"));
        }
        return ResponseEntity.ok(createSuccessResponse(teacher));
    }

    /**
     * 分页查询教师列表
     */
    @GetMapping("/page")
    public ResponseEntity<?> getTeacherPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            Teacher teacher) {

        Page<Teacher> page = new Page<>(pageNum, pageSize);
        IPage<Teacher> teacherPage = teacherService.getTeacherPage(page, teacher);
        return ResponseEntity.ok(createSuccessResponse(teacherPage));
    }

    /**
     * 根据学院ID查询教师
     */
    @GetMapping("/college/{collegeId}")
    public ResponseEntity<?> getTeachersByCollege(@PathVariable Long collegeId) {
        List<Teacher> teachers = teacherService.getByCollegeId(collegeId);
        return ResponseEntity.ok(createSuccessResponse(teachers));
    }

    /**
     * 根据专业ID查询教师
     */
    @GetMapping("/major/{majorId}")
    public ResponseEntity<?> getTeachersByMajor(@PathVariable Long majorId) {
        List<Teacher> teachers = teacherService.getByMajorId(majorId);
        return ResponseEntity.ok(createSuccessResponse(teachers));
    }

    /**
     * 创建教师信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createTeacher(@RequestBody Teacher teacher) {
        if (StrUtil.isEmpty(teacher.getTeacherNo())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("教师工号不能为空"));
        }

        // 检查教师工号是否已存在
        if (teacherService.getByTeacherNo(teacher.getTeacherNo()) != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(createErrorResponse("教师工号已存在"));
        }

        boolean saved = teacherService.save(teacher);
        if (saved) {
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(createSuccessResponse(teacher));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建教师失败"));
        }
    }

    /**
     * 更新教师信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'TEACHER')")
    public ResponseEntity<?> updateTeacher(
            @PathVariable Long id,
            @RequestBody Teacher teacher) {
        teacher.setId(id);
        boolean updated = teacherService.updateById(teacher);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse(teacher));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新教师失败"));
        }
    }

    /**
     * 更新教师状态
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateTeacherStatus(
            @PathVariable Long id,
            @RequestParam Integer status) {
        boolean updated = teacherService.updateTeacherStatus(id, status);
        if (updated) {
            return ResponseEntity.ok(createSuccessResponse("教师状态更新成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("更新教师状态失败"));
        }
    }

    /**
     * 删除教师信息
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteTeacher(@PathVariable Long id) {
        boolean deleted = teacherService.removeById(id);
        if (deleted) {
            return ResponseEntity.ok(createSuccessResponse("教师删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("删除教师失败"));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "操作成功");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}