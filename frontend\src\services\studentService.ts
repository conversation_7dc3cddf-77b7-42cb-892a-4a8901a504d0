import { api } from './api';
import type { Student, ApiResponse, PageResponse, PageRequest } from '../types';

export const studentService = {
  // 获取学生列表（分页）
  getStudentPage: async (params: PageRequest & Partial<Student>): Promise<PageResponse<Student>> => {
    const response = await api.get<ApiResponse<PageResponse<Student>>>('/students/page', {
      params: {
        pageNum: params.current || 1,
        pageSize: params.size || 10,
        ...params
      }
    });
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学生列表失败');
    }
  },

  // 根据ID获取学生详情
  getStudentById: async (id: number): Promise<Student> => {
    const response = await api.get<ApiResponse<Student>>(`/students/${id}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学生信息失败');
    }
  },

  // 根据学号获取学生信息
  getStudentByNo: async (studentNo: string): Promise<Student> => {
    const response = await api.get<ApiResponse<Student>>(`/students/student-no/${studentNo}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学生信息失败');
    }
  },

  // 创建学生
  createStudent: async (student: Omit<Student, 'id' | 'createTime' | 'updateTime'>): Promise<Student> => {
    const response = await api.post<ApiResponse<Student>>('/students', student);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '创建学生失败');
    }
  },

  // 更新学生信息
  updateStudent: async (id: number, student: Partial<Student>): Promise<Student> => {
    const response = await api.put<ApiResponse<Student>>(`/students/${id}`, student);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '更新学生信息失败');
    }
  },

  // 删除学生
  deleteStudent: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/students/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除学生失败');
    }
  },

  // 更新学生状态
  updateStudentStatus: async (id: number, status: number): Promise<void> => {
    const response = await api.patch<ApiResponse<void>>(`/students/${id}/status`, { status });
    
    if (!response.success) {
      throw new Error(response.message || '更新学生状态失败');
    }
  },

  // 根据学院ID获取学生列表
  getStudentsByCollege: async (collegeId: number): Promise<Student[]> => {
    const response = await api.get<ApiResponse<Student[]>>(`/students/college/${collegeId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取学院学生列表失败');
    }
  },

  // 根据专业ID获取学生列表
  getStudentsByMajor: async (majorId: number): Promise<Student[]> => {
    const response = await api.get<ApiResponse<Student[]>>(`/students/major/${majorId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取专业学生列表失败');
    }
  },

  // 根据班级ID获取学生列表
  getStudentsByClass: async (classId: number): Promise<Student[]> => {
    const response = await api.get<ApiResponse<Student[]>>(`/students/class/${classId}`);
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || '获取班级学生列表失败');
    }
  },

  // 批量导入学生
  importStudents: async (students: Omit<Student, 'id' | 'createTime' | 'updateTime'>[]): Promise<void> => {
    const response = await api.post<ApiResponse<void>>('/students/import', students);
    
    if (!response.success) {
      throw new Error(response.message || '批量导入学生失败');
    }
  }
};
